using System;
using System.Security.Cryptography;
using System.Text;

namespace 企业微信.Utils
{
    /// <summary>
    /// 加密解密帮助类
    /// </summary>
    public static class CryptoHelper
    {
        /// <summary>
        /// 计算MD5哈希值
        /// </summary>
        public static string ComputeMD5Hash(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            using (var md5 = MD5.Create())
            {
                var inputBytes = Encoding.UTF8.GetBytes(input);
                var hashBytes = md5.ComputeHash(inputBytes);
                return Convert.ToHexString(hashBytes).ToLowerInvariant();
            }
        }

        /// <summary>
        /// 计算SHA256哈希值
        /// </summary>
        public static string ComputeSHA256Hash(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            using (var sha256 = SHA256.Create())
            {
                var inputBytes = Encoding.UTF8.GetBytes(input);
                var hashBytes = sha256.ComputeHash(inputBytes);
                return Convert.ToHexString(hashBytes).ToLowerInvariant();
            }
        }

        /// <summary>
        /// Base64编码
        /// </summary>
        public static string ToBase64(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            var bytes = Encoding.UTF8.GetBytes(input);
            return Convert.ToBase64String(bytes);
        }

        /// <summary>
        /// Base64解码
        /// </summary>
        public static string FromBase64(string base64Input)
        {
            if (string.IsNullOrEmpty(base64Input))
                return string.Empty;

            try
            {
                var bytes = Convert.FromBase64String(base64Input);
                return Encoding.UTF8.GetString(bytes);
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// 生成随机字符串
        /// </summary>
        public static string GenerateRandomString(int length, bool includeNumbers = true, bool includeUppercase = true, bool includeLowercase = true, bool includeSpecialChars = false)
        {
            if (length <= 0)
                return string.Empty;

            var chars = new StringBuilder();
            
            if (includeLowercase)
                chars.Append("abcdefghijklmnopqrstuvwxyz");
            
            if (includeUppercase)
                chars.Append("ABCDEFGHIJKLMNOPQRSTUVWXYZ");
            
            if (includeNumbers)
                chars.Append("0123456789");
            
            if (includeSpecialChars)
                chars.Append("!@#$%^&*()_+-=[]{}|;:,.<>?");

            if (chars.Length == 0)
                throw new ArgumentException("至少需要包含一种字符类型");

            var charArray = chars.ToString().ToCharArray();
            var result = new StringBuilder(length);
            
            using (var rng = RandomNumberGenerator.Create())
            {
                var bytes = new byte[4];
                for (int i = 0; i < length; i++)
                {
                    rng.GetBytes(bytes);
                    var randomIndex = Math.Abs(BitConverter.ToInt32(bytes, 0)) % charArray.Length;
                    result.Append(charArray[randomIndex]);
                }
            }

            return result.ToString();
        }

        /// <summary>
        /// 生成GUID
        /// </summary>
        public static string GenerateGuid()
        {
            return Guid.NewGuid().ToString();
        }

        /// <summary>
        /// 生成短GUID（去掉连字符）
        /// </summary>
        public static string GenerateShortGuid()
        {
            return Guid.NewGuid().ToString("N");
        }

        /// <summary>
        /// AES加密
        /// </summary>
        public static string AESEncrypt(string plainText, string key)
        {
            if (string.IsNullOrEmpty(plainText) || string.IsNullOrEmpty(key))
                return string.Empty;

            try
            {
                using (var aes = Aes.Create())
                {
                    // 使用SHA256生成32字节密钥
                    var keyBytes = SHA256.HashData(Encoding.UTF8.GetBytes(key));
                    aes.Key = keyBytes;
                    aes.Mode = CipherMode.CBC;
                    aes.Padding = PaddingMode.PKCS7;

                    // 生成随机IV
                    aes.GenerateIV();

                    using (var encryptor = aes.CreateEncryptor())
                    {
                        var plainBytes = Encoding.UTF8.GetBytes(plainText);
                        var encryptedBytes = encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length);
                        
                        // 将IV和加密数据组合
                        var result = new byte[aes.IV.Length + encryptedBytes.Length];
                        Array.Copy(aes.IV, 0, result, 0, aes.IV.Length);
                        Array.Copy(encryptedBytes, 0, result, aes.IV.Length, encryptedBytes.Length);
                        
                        return Convert.ToBase64String(result);
                    }
                }
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// AES解密
        /// </summary>
        public static string AESDecrypt(string cipherText, string key)
        {
            if (string.IsNullOrEmpty(cipherText) || string.IsNullOrEmpty(key))
                return string.Empty;

            try
            {
                var cipherBytes = Convert.FromBase64String(cipherText);
                
                using (var aes = Aes.Create())
                {
                    // 使用SHA256生成32字节密钥
                    var keyBytes = SHA256.HashData(Encoding.UTF8.GetBytes(key));
                    aes.Key = keyBytes;
                    aes.Mode = CipherMode.CBC;
                    aes.Padding = PaddingMode.PKCS7;

                    // 提取IV
                    var iv = new byte[aes.BlockSize / 8];
                    Array.Copy(cipherBytes, 0, iv, 0, iv.Length);
                    aes.IV = iv;

                    // 提取加密数据
                    var encryptedData = new byte[cipherBytes.Length - iv.Length];
                    Array.Copy(cipherBytes, iv.Length, encryptedData, 0, encryptedData.Length);

                    using (var decryptor = aes.CreateDecryptor())
                    {
                        var decryptedBytes = decryptor.TransformFinalBlock(encryptedData, 0, encryptedData.Length);
                        return Encoding.UTF8.GetString(decryptedBytes);
                    }
                }
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// 简单字符串混淆（用于非敏感数据）
        /// </summary>
        public static string SimpleObfuscate(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            var bytes = Encoding.UTF8.GetBytes(input);
            for (int i = 0; i < bytes.Length; i++)
            {
                bytes[i] = (byte)(bytes[i] ^ 0xAA); // 简单异或
            }
            return Convert.ToBase64String(bytes);
        }

        /// <summary>
        /// 简单字符串反混淆
        /// </summary>
        public static string SimpleDeobfuscate(string obfuscated)
        {
            if (string.IsNullOrEmpty(obfuscated))
                return string.Empty;

            try
            {
                var bytes = Convert.FromBase64String(obfuscated);
                for (int i = 0; i < bytes.Length; i++)
                {
                    bytes[i] = (byte)(bytes[i] ^ 0xAA); // 简单异或
                }
                return Encoding.UTF8.GetString(bytes);
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// 验证哈希值
        /// </summary>
        public static bool VerifyHash(string input, string hash, HashAlgorithmType algorithm = HashAlgorithmType.SHA256)
        {
            if (string.IsNullOrEmpty(input) || string.IsNullOrEmpty(hash))
                return false;

            string computedHash = algorithm switch
            {
                HashAlgorithmType.MD5 => ComputeMD5Hash(input),
                HashAlgorithmType.SHA256 => ComputeSHA256Hash(input),
                _ => ComputeSHA256Hash(input)
            };

            return string.Equals(computedHash, hash, StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// 哈希算法类型
        /// </summary>
        public enum HashAlgorithmType
        {
            MD5,
            SHA256
        }

        /// <summary>
        /// 生成时间戳签名（用于API请求验证）
        /// </summary>
        public static string GenerateTimestampSignature(string data, string secret, long timestamp)
        {
            var signData = $"{data}|{timestamp}|{secret}";
            return ComputeSHA256Hash(signData);
        }

        /// <summary>
        /// 验证时间戳签名
        /// </summary>
        public static bool VerifyTimestampSignature(string data, string secret, long timestamp, string signature, int toleranceSeconds = 300)
        {
            // 检查时间戳是否在容忍范围内
            var currentTimestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            if (Math.Abs(currentTimestamp - timestamp) > toleranceSeconds)
                return false;

            var expectedSignature = GenerateTimestampSignature(data, secret, timestamp);
            return string.Equals(expectedSignature, signature, StringComparison.OrdinalIgnoreCase);
        }
    }
}