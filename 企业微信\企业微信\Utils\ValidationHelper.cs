using System;
using System.Text.RegularExpressions;
using System.Net;

namespace 企业微信.Utils
{
    /// <summary>
    /// 验证帮助类
    /// </summary>
    public static class ValidationHelper
    {
        /// <summary>
        /// 验证字符串是否为空或空白
        /// </summary>
        public static bool IsNullOrWhiteSpace(string value)
        {
            return string.IsNullOrWhiteSpace(value);
        }

        /// <summary>
        /// 验证字符串是否不为空且不为空白
        /// </summary>
        public static bool IsNotNullOrWhiteSpace(string value)
        {
            return !string.IsNullOrWhiteSpace(value);
        }

        /// <summary>
        /// 验证URL格式是否正确
        /// </summary>
        public static bool IsValidUrl(string url)
        {
            if (IsNullOrWhiteSpace(url))
                return false;

            try
            {
                var uri = new Uri(url);
                return uri.Scheme == Uri.UriSchemeHttp || uri.Scheme == Uri.UriSchemeHttps;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证API Key格式（基本格式检查）
        /// </summary>
        public static bool IsValidApiKey(string apiKey)
        {
            if (IsNullOrWhiteSpace(apiKey))
                return false;

            // API Key通常是32-128位的字母数字组合
            return apiKey.Length >= 16 && apiKey.Length <= 128 && 
                   Regex.IsMatch(apiKey, @"^[a-zA-Z0-9\-_]+$");
        }

        /// <summary>
        /// 验证企业微信ID格式
        /// </summary>
        public static bool IsValidCorpId(string corpId)
        {
            if (IsNullOrWhiteSpace(corpId))
                return false;

            // 企业微信ID通常是18位字母数字组合
            return corpId.Length >= 10 && corpId.Length <= 32 && 
                   Regex.IsMatch(corpId, @"^[a-zA-Z0-9]+$");
        }

        /// <summary>
        /// 验证企业微信Secret格式
        /// </summary>
        public static bool IsValidCorpSecret(string corpSecret)
        {
            if (IsNullOrWhiteSpace(corpSecret))
                return false;

            // 企业微信Secret通常是32-64位字母数字组合
            return corpSecret.Length >= 20 && corpSecret.Length <= 128 && 
                   Regex.IsMatch(corpSecret, @"^[a-zA-Z0-9\-_]+$");
        }

        /// <summary>
        /// 验证企业微信Agent ID格式
        /// </summary>
        public static bool IsValidAgentId(string agentId)
        {
            if (IsNullOrWhiteSpace(agentId))
                return false;

            // Agent ID通常是数字
            return Regex.IsMatch(agentId, @"^\d+$") && agentId.Length <= 10;
        }

        /// <summary>
        /// 验证端口号
        /// </summary>
        public static bool IsValidPort(int port)
        {
            return port > 0 && port <= 65535;
        }

        /// <summary>
        /// 验证端口号字符串
        /// </summary>
        public static bool IsValidPort(string port)
        {
            if (IsNullOrWhiteSpace(port))
                return false;

            return int.TryParse(port, out int portNumber) && IsValidPort(portNumber);
        }

        /// <summary>
        /// 验证IP地址格式
        /// </summary>
        public static bool IsValidIpAddress(string ipAddress)
        {
            if (IsNullOrWhiteSpace(ipAddress))
                return false;

            return IPAddress.TryParse(ipAddress, out _);
        }

        /// <summary>
        /// 验证文件路径格式
        /// </summary>
        public static bool IsValidFilePath(string filePath)
        {
            if (IsNullOrWhiteSpace(filePath))
                return false;

            try
            {
                // 检查路径是否包含非法字符
                var invalidChars = System.IO.Path.GetInvalidPathChars();
                foreach (var invalidChar in invalidChars)
                {
                    if (filePath.Contains(invalidChar))
                        return false;
                }

                // 尝试获取完整路径
                System.IO.Path.GetFullPath(filePath);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证文件名格式
        /// </summary>
        public static bool IsValidFileName(string fileName)
        {
            if (IsNullOrWhiteSpace(fileName))
                return false;

            try
            {
                // 检查文件名是否包含非法字符
                var invalidChars = System.IO.Path.GetInvalidFileNameChars();
                foreach (var invalidChar in invalidChars)
                {
                    if (fileName.Contains(invalidChar))
                        return false;
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证数字范围
        /// </summary>
        public static bool IsInRange(int value, int min, int max)
        {
            return value >= min && value <= max;
        }

        /// <summary>
        /// 验证数字范围
        /// </summary>
        public static bool IsInRange(double value, double min, double max)
        {
            return value >= min && value <= max;
        }

        /// <summary>
        /// 验证字符串长度范围
        /// </summary>
        public static bool IsValidLength(string value, int minLength, int maxLength)
        {
            if (value == null)
                return minLength <= 0;

            return value.Length >= minLength && value.Length <= maxLength;
        }

        /// <summary>
        /// 验证正整数
        /// </summary>
        public static bool IsPositiveInteger(string value)
        {
            if (IsNullOrWhiteSpace(value))
                return false;

            return int.TryParse(value, out int number) && number > 0;
        }

        /// <summary>
        /// 验证非负整数
        /// </summary>
        public static bool IsNonNegativeInteger(string value)
        {
            if (IsNullOrWhiteSpace(value))
                return false;

            return int.TryParse(value, out int number) && number >= 0;
        }

        /// <summary>
        /// 验证布尔值字符串
        /// </summary>
        public static bool IsValidBoolean(string value)
        {
            if (IsNullOrWhiteSpace(value))
                return false;

            return bool.TryParse(value, out _) || 
                   value.Equals("1", StringComparison.OrdinalIgnoreCase) ||
                   value.Equals("0", StringComparison.OrdinalIgnoreCase) ||
                   value.Equals("yes", StringComparison.OrdinalIgnoreCase) ||
                   value.Equals("no", StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// 验证JSON格式
        /// </summary>
        public static bool IsValidJson(string json)
        {
            if (IsNullOrWhiteSpace(json))
                return false;

            try
            {
                Newtonsoft.Json.JsonConvert.DeserializeObject(json);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证正则表达式格式
        /// </summary>
        public static bool IsValidRegex(string pattern)
        {
            if (IsNullOrWhiteSpace(pattern))
                return false;

            try
            {
                new Regex(pattern);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证时间间隔（毫秒）
        /// </summary>
        public static bool IsValidTimeInterval(int milliseconds)
        {
            return milliseconds >= 0 && milliseconds <= int.MaxValue;
        }

        /// <summary>
        /// 验证时间间隔字符串（毫秒）
        /// </summary>
        public static bool IsValidTimeInterval(string milliseconds)
        {
            if (IsNullOrWhiteSpace(milliseconds))
                return false;

            return int.TryParse(milliseconds, out int ms) && IsValidTimeInterval(ms);
        }

        /// <summary>
        /// 验证日期时间格式
        /// </summary>
        public static bool IsValidDateTime(string dateTime)
        {
            if (IsNullOrWhiteSpace(dateTime))
                return false;

            return DateTime.TryParse(dateTime, out _);
        }

        /// <summary>
        /// 验证GUID格式
        /// </summary>
        public static bool IsValidGuid(string guid)
        {
            if (IsNullOrWhiteSpace(guid))
                return false;

            return Guid.TryParse(guid, out _);
        }

        /// <summary>
        /// 验证Base64格式
        /// </summary>
        public static bool IsValidBase64(string base64)
        {
            if (IsNullOrWhiteSpace(base64))
                return false;

            try
            {
                Convert.FromBase64String(base64);
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}