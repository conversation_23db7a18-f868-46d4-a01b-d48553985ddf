using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.IO;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using AntdUI;
using 企业微信.Services;
using 企业微信.Models;
using 企业微信.Config;
using WinFormsTimer = System.Windows.Forms.Timer;

namespace 企业微信
{
    public partial class Form1 : Form
    {
        #region 私有字段
        private IServiceController _serviceController;
        private IConfigManager _configManager;
        private ILogManager _logManager;
        private bool _isServiceRunning = false;
        private WinFormsTimer _statusUpdateTimer;
        private List<MessageModel> _recentMessages = new List<MessageModel>();
        
        // 新增字段用于简化实现
        private WinFormsTimer uptimeTimer;
        private DateTime serviceStartTime;
        private int totalMessages = 0;
        private int successMessages = 0;
        private int failedMessages = 0;
        private readonly string configFilePath = Path.Combine(Application.StartupPath, "config.json");
        private AppConfig simpleConfig;
        #endregion

        #region 构造函数
        public Form1()
        {
            InitializeComponent();
            InitializeServices();
            InitializeUI();
            InitializeTimer();
            InitializeSimpleFeatures();
        }
        #endregion

        #region 初始化方法
        /// <summary>
        /// 初始化服务
        /// </summary>
        private void InitializeServices()
        {
            try
            {
                _configManager = new ConfigManager();
                _logManager = new LogManager(_configManager);
                
                // 创建依赖服务
                var difyClient = new DifyClient(_configManager, _logManager);
                var wechatBridge = new WeChatBridge(_configManager, _logManager);
                var messageProcessor = new MessageProcessor(_configManager, _logManager, difyClient, wechatBridge);
                
                _serviceController = new ServiceController(_configManager, _logManager, difyClient, wechatBridge, messageProcessor);

                // 订阅事件
                _serviceController.StatusChanged += OnServiceStatusChanged;
                _serviceController.MessageReceived += OnMessageReceived;
                _serviceController.ErrorOccurred += OnErrorOccurred;

                _logManager.LogAdded += OnLogAdded;
            }
            catch (Exception ex)
            {
                AntdUI.Message.error(this, $"初始化服务失败: {ex.Message}", autoClose: 5);
            }
        }

        /// <summary>
        /// 初始化UI
        /// </summary>
        private void InitializeUI()
        {
            // 设置窗体属性
            this.Text = AppConstants.APP_NAME;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimumSize = new Size(1000, 700);

            // 加载配置到UI
            LoadConfigToUI();
            
            // 更新状态显示
            UpdateServiceStatus();
            UpdateConnectionStatus();
        }

        /// <summary>
        /// 初始化定时器
        /// </summary>
        private void InitializeTimer()
        {
            _statusUpdateTimer = new WinFormsTimer();
            _statusUpdateTimer.Interval = 5000; // 5秒更新一次
            _statusUpdateTimer.Tick += OnStatusUpdateTimer_Tick;
            _statusUpdateTimer.Start();
        }
        
        /// <summary>
        /// 初始化简化功能
        /// </summary>
        private void InitializeSimpleFeatures()
        {
            // 初始化简化配置
            LoadSimpleConfiguration();
            
            // 初始化运行时间定时器
            uptimeTimer = new WinFormsTimer();
            uptimeTimer.Interval = 1000; // 1秒更新一次
            uptimeTimer.Tick += UptimeTimer_Tick;
            
            // 加载配置到界面
            LoadSimpleConfigurationToUI();
            
            // 添加日志
            AddSimpleLog("应用程序已启动");
        }
        #endregion

        #region 事件处理
        /// <summary>
        /// 服务状态变更事件
        /// </summary>
        private void OnServiceStatusChanged(object sender, ServiceStatusChangedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnServiceStatusChanged(sender, e)));
                return;
            }

            _isServiceRunning = e.Status == ServiceStatus.Running;
            UpdateServiceStatus();
            
            string message;
            switch (e.Status)
            {
                case ServiceStatus.Running:
                    message = "服务已启动";
                    break;
                case ServiceStatus.Stopped:
                    message = "服务已停止";
                    break;
                case ServiceStatus.Error:
                    message = $"服务错误: {e.Message}";
                    break;
                default:
                    message = $"服务状态: {e.Status}";
                    break;
            }

            AntdUI.Message.info(this, message, autoClose: 3);
        }

        /// <summary>
        /// 消息接收事件
        /// </summary>
        private void OnMessageReceived(object sender, MessageReceivedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnMessageReceived(sender, e)));
                return;
            }

            // 添加到最近消息列表
            _recentMessages.Insert(0, e.Message);
            if (_recentMessages.Count > 100) // 保持最近100条消息
            {
                _recentMessages.RemoveAt(_recentMessages.Count - 1);
            }

            // 更新消息列表显示
            UpdateMessageList();
        }

        /// <summary>
        /// 错误发生事件
        /// </summary>
        private void OnErrorOccurred(object sender, ErrorOccurredEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnErrorOccurred(sender, e)));
                return;
            }

            AntdUI.Message.error(this, $"发生错误: {e.ErrorMessage}", autoClose: 5);
        }

        /// <summary>
        /// 日志添加事件
        /// </summary>
        private void OnLogAdded(object sender, LogAddedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnLogAdded(sender, e)));
                return;
            }

            // 更新日志显示
            var logEntry = new LogEntry
            {
                Timestamp = e.Timestamp,
                Level = e.Level,
                Message = e.Message,
                Source = e.Source
            };
            UpdateLogDisplay(logEntry);
        }

        /// <summary>
        /// 状态更新定时器事件
        /// </summary>
        private void OnStatusUpdateTimer_Tick(object sender, EventArgs e)
        {
            UpdateConnectionStatus();
            UpdateStatistics();
        }
        #endregion

        #region UI更新方法
        /// <summary>
        /// 加载配置到UI
        /// </summary>
        private void LoadConfigToUI()
        {
            try
            {
                var config = _configManager.GetConfig();
                
                // 更新Dify配置
                txtDifyApiUrl.Text = config.Dify.ApiUrl;
                txtDifyApiKey.Text = config.Dify.ApiKey;
                numDifyTimeout.Value = config.Dify.TimeoutSeconds;

                // 更新企业微信配置
                txtWeChatCorpId.Text = config.WeChat.CorpId;
                txtWeChatCorpSecret.Text = config.WeChat.CorpSecret;
                txtWeChatAgentId.Text = config.WeChat.AgentId;

                // 更新应用设置
                chkAutoStart.Checked = config.App.AutoStart;
                chkMinimizeToTray.Checked = config.App.MinimizeToTray;
                numLogRetentionDays.Value = config.App.LogRetentionDays;
            }
            catch (Exception ex)
            {
                AntdUI.Message.error(this, $"加载配置失败: {ex.Message}", autoClose: 5);
            }
        }

        /// <summary>
        /// 保存UI配置
        /// </summary>
        private void SaveConfigFromUI()
        {
            try
            {
                var config = _configManager.GetConfig();
                
                // 更新Dify配置
                config.Dify.ApiUrl = txtDifyApiUrl.Text.Trim();
                config.Dify.ApiKey = txtDifyApiKey.Text.Trim();
                config.Dify.TimeoutSeconds = (int)numDifyTimeout.Value;

                // 更新企业微信配置
                config.WeChat.CorpId = txtWeChatCorpId.Text.Trim();
                config.WeChat.CorpSecret = txtWeChatCorpSecret.Text.Trim();
                config.WeChat.AgentId = txtWeChatAgentId.Text.Trim();

                // 更新应用设置
                config.App.AutoStart = chkAutoStart.Checked;
                config.App.MinimizeToTray = chkMinimizeToTray.Checked;
                config.App.LogRetentionDays = (int)numLogRetentionDays.Value;
                
                var success = _configManager.SaveConfig(config);
                if (success)
                {
                    AntdUI.Message.success(this, "配置保存成功", autoClose: 3);
                }
                else
                {
                    AntdUI.Message.error(this, "配置保存失败", autoClose: 5);
                }
            }
            catch (Exception ex)
            {
                AntdUI.Message.error(this, $"保存配置失败: {ex.Message}", autoClose: 5);
            }
        }

        /// <summary>
        /// 更新服务状态显示
        /// </summary>
        private void UpdateServiceStatus()
        {
            if (_isServiceRunning)
            {
                lblServiceStatus.Text = "运行中";
                lblServiceStatus.ForeColor = Color.Green;
                btnStartService.Enabled = false;
                btnStopService.Enabled = true;
                btnRestartService.Enabled = true;
            }
            else
            {
                lblServiceStatus.Text = "已停止";
                lblServiceStatus.ForeColor = Color.Red;
                btnStartService.Enabled = true;
                btnStopService.Enabled = false;
                btnRestartService.Enabled = false;
            }
        }

        /// <summary>
        /// 更新连接状态
        /// </summary>
        private async void UpdateConnectionStatus()
        {
            try
            {
                if (_serviceController != null)
                {
                    var difyStatus = await _serviceController.CheckDifyConnectionAsync();
                    var wechatStatus = await _serviceController.CheckWeChatConnectionAsync();
                    
                    lblDifyStatus.Text = difyStatus.IsConnected ? "已连接" : "连接失败";
                    lblDifyStatus.ForeColor = difyStatus.IsConnected ? Color.Green : Color.Red;
                    
                    lblWeChatStatus.Text = wechatStatus.IsConnected ? "已连接" : "连接失败";
                    lblWeChatStatus.ForeColor = wechatStatus.IsConnected ? Color.Green : Color.Red;
                }
            }
            catch (Exception ex)
            {
                _logManager?.LogError($"更新连接状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新统计信息
        /// </summary>
        private void UpdateStatistics()
        {
            try
            {
                if (_serviceController != null)
                {
                    var stats = _serviceController.GetStatistics();
                    lblTotalMessages.Text = stats.TotalMessages.ToString();
                    lblSuccessMessages.Text = stats.SuccessMessages.ToString();
                    lblFailedMessages.Text = stats.FailedMessages.ToString();
                    lblUptime.Text = stats.Uptime.ToString(@"hh\:mm\:ss");
                }
            }
            catch (Exception ex)
            {
                _logManager?.LogError($"更新统计信息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新消息列表
        /// </summary>
        private void UpdateMessageList()
        {
            try
            {
                // 清空现有项目
                listMessages.Items.Clear();
                
                // 添加最近消息
                foreach (var message in _recentMessages.Take(50))
                {
                    var item = new ListViewItem(message.CreatedAt.ToString("HH:mm:ss"));
                    item.SubItems.Add(message.SenderId);
                    item.SubItems.Add(message.Content.Length > 50 ? message.Content.Substring(0, 50) + "..." : message.Content);
                    item.SubItems.Add(message.Status.ToString());
                    item.Tag = message;
                    
                    listMessages.Items.Add(item);
                }
            }
            catch (Exception ex)
            {
                _logManager?.LogError($"更新消息列表失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新日志显示
        /// </summary>
        private void UpdateLogDisplay(LogEntry logEntry)
        {
            try
            {
                // 限制日志显示数量
                if (txtLogs.Text.Split('\n').Length > 1000)
                {
                    var lines = txtLogs.Text.Split('\n').Skip(100).ToArray();
                    txtLogs.Text = string.Join("\n", lines);
                }
                
                // 添加新日志
                var logText = $"[{logEntry.Timestamp:HH:mm:ss}] [{logEntry.Level}] {logEntry.Message}";
                txtLogs.AppendText(logText + Environment.NewLine);
                
                // 滚动到底部
                txtLogs.SelectionStart = txtLogs.Text.Length;
                txtLogs.ScrollToCaret();
            }
            catch (Exception ex)
            {
                // 避免日志更新失败导致的循环错误
                System.Diagnostics.Debug.WriteLine($"更新日志显示失败: {ex.Message}");
            }
        }
        #endregion

        #region 按钮事件处理
        /// <summary>
        /// 启动服务
        /// </summary>
        private async void btnStartService_Click(object sender, EventArgs e)
        {
            try
            {
                btnStartService.Enabled = false;
                await _serviceController.StartAsync();
                AntdUI.Message.success(this, "服务启动成功", autoClose: 3);
            }
            catch (Exception ex)
            {
                AntdUI.Message.error(this, $"启动服务失败: {ex.Message}", autoClose: 5);
                btnStartService.Enabled = true;
            }
        }

        /// <summary>
        /// 停止服务
        /// </summary>
        private async void btnStopService_Click(object sender, EventArgs e)
        {
            try
            {
                btnStopService.Enabled = false;
                await _serviceController.StopAsync();
                AntdUI.Message.success(this, "服务停止成功", autoClose: 3);
            }
            catch (Exception ex)
            {
                AntdUI.Message.error(this, $"停止服务失败: {ex.Message}", autoClose: 5);
                btnStopService.Enabled = true;
            }
        }

        /// <summary>
        /// 重启服务
        /// </summary>
        private async void btnRestartService_Click(object sender, EventArgs e)
        {
            try
            {
                btnRestartService.Enabled = false;
                await _serviceController.RestartAsync();
                AntdUI.Message.success(this, "服务重启成功", autoClose: 3);
            }
            catch (Exception ex)
            {
                AntdUI.Message.error(this, $"重启服务失败: {ex.Message}", autoClose: 5);
            }
            finally
            {
                btnRestartService.Enabled = true;
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        private void btnSaveConfig_Click(object sender, EventArgs e)
        {
            SaveConfigFromUI();
        }

        /// <summary>
        /// 测试Dify连接
        /// </summary>
        private async void btnTestDify_Click(object sender, EventArgs e)
        {
            try
            {
                btnTestDify.Enabled = false;
                
                // 优先使用服务控制器
                if (_serviceController != null)
                {
                    var result = await _serviceController.CheckDifyConnectionAsync();
                    
                    if (result.IsConnected)
                    {
                        AntdUI.Message.success(this, "Dify连接测试成功", autoClose: 3);
                    }
                    else
                    {
                        AntdUI.Message.error(this, $"Dify连接测试失败: {result.Message}", autoClose: 5);
                    }
                }
                else
                {
                    // 备用简化实现
                    await TestDifyConnectionSimple();
                }
            }
            catch (Exception ex)
            {
                AntdUI.Message.error(this, $"测试连接失败: {ex.Message}", autoClose: 5);
            }
            finally
            {
                btnTestDify.Enabled = true;
            }
        }

        /// <summary>
        /// 测试企业微信连接
        /// </summary>
        private async void btnTestWeChat_Click(object sender, EventArgs e)
        {
            try
            {
                btnTestWeChat.Enabled = false;
                
                // 优先使用服务控制器
                if (_serviceController != null)
                {
                    var result = await _serviceController.CheckWeChatConnectionAsync();
                    
                    if (result.IsConnected)
                    {
                        AntdUI.Message.success(this, "企业微信连接测试成功", autoClose: 3);
                    }
                    else
                    {
                        AntdUI.Message.error(this, $"企业微信连接测试失败: {result.Message}", autoClose: 5);
                    }
                }
                else
                {
                    // 备用简化实现
                    await TestWeChatConnectionSimple();
                }
            }
            catch (Exception ex)
            {
                AntdUI.Message.error(this, $"测试连接失败: {ex.Message}", autoClose: 5);
            }
            finally
            {
                btnTestWeChat.Enabled = true;
            }
        }

        /// <summary>
        /// 清空日志
        /// </summary>
        private void btnClearLogs_Click(object sender, EventArgs e)
        {
            txtLogs.Clear();
            AntdUI.Message.info(this, "日志已清空", autoClose: 2);
        }
        #endregion

        #region 窗体事件
        /// <summary>
        /// 窗体关闭事件
        /// </summary>
        protected override async void OnFormClosing(FormClosingEventArgs e)
        {
            try
            {
                if (_isServiceRunning)
                {
                    var result = AntdUI.Modal.open(this, "确认", "服务正在运行，是否停止服务并退出？", TType.Warn);
                    if (result == DialogResult.OK)
                    {
                        await _serviceController?.StopAsync();
                    }
                    else
                    {
                        e.Cancel = true;
                        return;
                    }
                }

                // 停止定时器
                _statusUpdateTimer?.Stop();
                _statusUpdateTimer?.Dispose();

                // 释放服务资源
                _serviceController?.Dispose();
                _logManager?.Dispose();
            }
            catch (Exception ex)
            {
                AntdUI.Message.error(this, $"关闭应用失败: {ex.Message}", autoClose: 3);
            }

            base.OnFormClosing(e);
        }
        #endregion
        
        #region 简化功能实现
        
        private void LoadSimpleConfiguration()
        {
            try
            {
                if (File.Exists(configFilePath))
                {
                    string json = File.ReadAllText(configFilePath);
                    simpleConfig = JsonSerializer.Deserialize<AppConfig>(json) ?? new AppConfig();
                }
                else
                {
                    simpleConfig = new AppConfig();
                }
            }
            catch (Exception ex)
            {
                simpleConfig = new AppConfig();
                AddSimpleLog($"加载配置失败: {ex.Message}");
            }
        }
        
        private void LoadSimpleConfigurationToUI()
        {
            if (simpleConfig != null)
            {
                txtDifyApiUrl.Text = simpleConfig.Dify.ApiUrl;
                txtDifyApiKey.Text = simpleConfig.Dify.ApiKey;
                numDifyTimeout.Value = simpleConfig.Dify.TimeoutSeconds;

                txtWeChatCorpId.Text = simpleConfig.WeChat.CorpId;
                txtWeChatCorpSecret.Text = simpleConfig.WeChat.CorpSecret;
                txtWeChatAgentId.Text = simpleConfig.WeChat.AgentId;

                chkAutoStart.Checked = simpleConfig.App.AutoStart;
                chkMinimizeToTray.Checked = simpleConfig.App.MinimizeToTray;
                numLogRetentionDays.Value = simpleConfig.App.LogRetentionDays;
            }
        }
        
        private void SaveSimpleConfiguration()
        {
            try
            {
                if (simpleConfig != null)
                {
                    simpleConfig.Dify.ApiUrl = txtDifyApiUrl.Text.Trim();
                    simpleConfig.Dify.ApiKey = txtDifyApiKey.Text.Trim();
                    simpleConfig.Dify.TimeoutSeconds = (int)numDifyTimeout.Value;

                    simpleConfig.WeChat.CorpId = txtWeChatCorpId.Text.Trim();
                    simpleConfig.WeChat.CorpSecret = txtWeChatCorpSecret.Text.Trim();
                    simpleConfig.WeChat.AgentId = txtWeChatAgentId.Text.Trim();

                    simpleConfig.App.AutoStart = chkAutoStart.Checked;
                    simpleConfig.App.MinimizeToTray = chkMinimizeToTray.Checked;
                    simpleConfig.App.LogRetentionDays = (int)numLogRetentionDays.Value;
                    
                    string json = JsonSerializer.Serialize(simpleConfig, new JsonSerializerOptions 
                    { 
                        WriteIndented = true,
                        Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                    });
                    File.WriteAllText(configFilePath, json);
                    AddSimpleLog("配置已保存");
                }
            }
            catch (Exception ex)
            {
                AddSimpleLog($"保存配置失败: {ex.Message}");
            }
        }
        
        private async Task TestDifyConnectionSimple()
        {
            if (string.IsNullOrWhiteSpace(txtDifyApiUrl.Text) || string.IsNullOrWhiteSpace(txtDifyApiKey.Text))
            {
                AntdUI.Message.warn(this, "请先填写 Dify API 地址和密钥", autoClose: 3);
                return;
            }

            lblDifyStatus.Text = "测试中...";
            lblDifyStatus.ForeColor = Color.Orange;

            try
            {
                using (var client = new HttpClient())
                {
                    client.Timeout = TimeSpan.FromSeconds((int)numDifyTimeout.Value);
                    client.DefaultRequestHeaders.Add("Authorization", $"Bearer {txtDifyApiKey.Text}");
                    
                    var response = await client.GetAsync($"{txtDifyApiUrl.Text.TrimEnd('/')}/health");
                    
                    if (response.IsSuccessStatusCode)
                    {
                        lblDifyStatus.Text = "连接成功";
                        lblDifyStatus.ForeColor = Color.Green;
                        AntdUI.Message.success(this, "Dify 连接测试成功", autoClose: 2);
                        AddSimpleLog("Dify 连接测试成功");
                    }
                    else
                    {
                        lblDifyStatus.Text = "连接失败";
                        lblDifyStatus.ForeColor = Color.Red;
                        AntdUI.Message.error(this, $"Dify 连接失败: {response.StatusCode}", autoClose: 3);
                        AddSimpleLog($"Dify 连接失败: {response.StatusCode}");
                    }
                }
            }
            catch (Exception ex)
            {
                lblDifyStatus.Text = "连接失败";
                lblDifyStatus.ForeColor = Color.Red;
                AntdUI.Message.error(this, $"Dify 连接测试失败: {ex.Message}", autoClose: 3);
                AddSimpleLog($"Dify 连接测试失败: {ex.Message}");
            }
        }
        
        private async Task TestWeChatConnectionSimple()
        {
            if (string.IsNullOrWhiteSpace(txtWeChatCorpId.Text) || 
                string.IsNullOrWhiteSpace(txtWeChatCorpSecret.Text) || 
                string.IsNullOrWhiteSpace(txtWeChatAgentId.Text))
            {
                AntdUI.Message.warn(this, "请先填写完整的企业微信配置", autoClose: 3);
                return;
            }

            lblWeChatStatus.Text = "测试中...";
            lblWeChatStatus.ForeColor = Color.Orange;

            try
            {
                using (var client = new HttpClient())
                {
                    var tokenUrl = $"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={txtWeChatCorpId.Text}&corpsecret={txtWeChatCorpSecret.Text}";
                    var response = await client.GetAsync(tokenUrl);
                    var content = await response.Content.ReadAsStringAsync();
                    
                    var tokenResult = JsonSerializer.Deserialize<JsonElement>(content);
                    
                    if (tokenResult.TryGetProperty("access_token", out var token))
                    {
                        lblWeChatStatus.Text = "连接成功";
                        lblWeChatStatus.ForeColor = Color.Green;
                        AntdUI.Message.success(this, "企业微信连接测试成功", autoClose: 2);
                        AddSimpleLog("企业微信连接测试成功");
                    }
                    else
                    {
                        var errcode = tokenResult.TryGetProperty("errcode", out var code) ? code.GetInt32() : -1;
                        var errmsg = tokenResult.TryGetProperty("errmsg", out var msg) ? msg.GetString() : "未知错误";
                        
                        lblWeChatStatus.Text = "连接失败";
                        lblWeChatStatus.ForeColor = Color.Red;
                        AntdUI.Message.error(this, $"企业微信连接失败: {errmsg} ({errcode})", autoClose: 3);
                        AddSimpleLog($"企业微信连接失败: {errmsg} ({errcode})");
                    }
                }
            }
            catch (Exception ex)
            {
                lblWeChatStatus.Text = "连接失败";
                lblWeChatStatus.ForeColor = Color.Red;
                AntdUI.Message.error(this, $"企业微信连接测试失败: {ex.Message}", autoClose: 3);
                AddSimpleLog($"企业微信连接测试失败: {ex.Message}");
            }
        }
        
        private void UptimeTimer_Tick(object sender, EventArgs e)
        {
            if (_isServiceRunning)
            {
                var uptime = DateTime.Now - serviceStartTime;
                lblUptime.Text = $"{uptime.Days:D2}:{uptime.Hours:D2}:{uptime.Minutes:D2}:{uptime.Seconds:D2}";
            }
        }
        
        private void AddSimpleLog(string message)
        {
            var logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}";
            
            if (InvokeRequired)
            {
                Invoke(new Action<string>(AddSimpleLogInternal), logEntry);
            }
            else
            {
                AddSimpleLogInternal(logEntry);
            }
        }
        
        private void AddSimpleLogInternal(string logEntry)
        {
            if (txtLogs != null)
            {
                txtLogs.Text += logEntry + Environment.NewLine;
                
                txtLogs.SelectionStart = txtLogs.Text.Length;
                txtLogs.ScrollToCaret();
                
                var lines = txtLogs.Text.Split('\n');
                if (lines.Length > 1000)
                {
                    txtLogs.Text = string.Join("\n", lines.Skip(lines.Length - 800));
                }
            }
        }
        
        #endregion
    }
    

}
