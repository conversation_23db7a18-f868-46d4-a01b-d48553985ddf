using System;
using System.Threading.Tasks;

namespace 企业微信.Models
{
    /// <summary>
    /// 服务控制器接口
    /// </summary>
    public interface IServiceController : IDisposable
    {
        /// <summary>
        /// 服务状态变更事件
        /// </summary>
        event EventHandler<ServiceStatusChangedEventArgs> ServiceStatusChanged;

        /// <summary>
        /// 服务状态变更事件（兼容性）
        /// </summary>
        event EventHandler<ServiceStatusChangedEventArgs> StatusChanged;

        /// <summary>
        /// 消息接收事件
        /// </summary>
        event EventHandler<MessageReceivedEventArgs> MessageReceived;

        /// <summary>
        /// 错误发生事件
        /// </summary>
        event EventHandler<ErrorOccurredEventArgs> ErrorOccurred;

        /// <summary>
        /// 当前服务状态
        /// </summary>
        ServiceStatus Status { get; }

        /// <summary>
        /// 是否正在运行
        /// </summary>
        bool IsRunning { get; }

        /// <summary>
        /// 启动服务
        /// </summary>
        Task<bool> StartAsync();

        /// <summary>
        /// 停止服务
        /// </summary>
        Task<bool> StopAsync();

        /// <summary>
        /// 重启服务
        /// </summary>
        Task<bool> RestartAsync();

        /// <summary>
        /// 检查Dify连接
        /// </summary>
        Task<ConnectionResult> CheckDifyConnectionAsync();

        /// <summary>
        /// 检查企业微信连接
        /// </summary>
        Task<ConnectionResult> CheckWeChatConnectionAsync();

        /// <summary>
        /// 获取运行统计信息
        /// </summary>
        ServiceStatistics GetStatistics();

        /// <summary>
        /// 发送消息
        /// </summary>
        Task<bool> SendMessageAsync(string message, string userId = null);
    }

    /// <summary>
    /// 服务统计信息
    /// </summary>
    public class ServiceStatistics
    {
        public TimeSpan Uptime { get; set; }
        public int MessagesReceived { get; set; }
        public int MessagesSent { get; set; }
        public int ErrorCount { get; set; }
        public DateTime LastActivity { get; set; }
        public bool DifyConnected { get; set; }
        public bool WeChatConnected { get; set; }

        // 兼容性属性
        public int TotalMessages => MessagesReceived + MessagesSent;
        public int SuccessMessages => MessagesSent;
        public int FailedMessages => ErrorCount;
    }
}