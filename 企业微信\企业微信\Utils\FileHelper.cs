using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace 企业微信.Utils
{
    /// <summary>
    /// 文件操作帮助类
    /// </summary>
    public static class FileHelper
    {
        /// <summary>
        /// 确保目录存在
        /// </summary>
        public static void EnsureDirectoryExists(string directoryPath)
        {
            if (string.IsNullOrWhiteSpace(directoryPath))
                throw new ArgumentException("目录路径不能为空", nameof(directoryPath));

            if (!Directory.Exists(directoryPath))
            {
                Directory.CreateDirectory(directoryPath);
            }
        }

        /// <summary>
        /// 安全读取文件内容
        /// </summary>
        public static async Task<string> ReadTextFileAsync(string filePath, Encoding encoding = null)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));

            if (!File.Exists(filePath))
                throw new FileNotFoundException($"文件不存在: {filePath}");

            try
            {
                encoding = encoding ?? Encoding.UTF8;
                return await File.ReadAllTextAsync(filePath, encoding);
            }
            catch (Exception ex)
            {
                throw new IOException($"读取文件失败: {filePath}", ex);
            }
        }

        /// <summary>
        /// 安全写入文件内容
        /// </summary>
        public static async Task WriteTextFileAsync(string filePath, string content, Encoding encoding = null, bool createDirectory = true)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));

            try
            {
                if (createDirectory)
                {
                    var directory = Path.GetDirectoryName(filePath);
                    if (!string.IsNullOrEmpty(directory))
                    {
                        EnsureDirectoryExists(directory);
                    }
                }

                encoding = encoding ?? Encoding.UTF8;
                await File.WriteAllTextAsync(filePath, content ?? string.Empty, encoding);
            }
            catch (Exception ex)
            {
                throw new IOException($"写入文件失败: {filePath}", ex);
            }
        }

        /// <summary>
        /// 安全追加文件内容
        /// </summary>
        public static async Task AppendTextFileAsync(string filePath, string content, Encoding encoding = null, bool createDirectory = true)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));

            try
            {
                if (createDirectory)
                {
                    var directory = Path.GetDirectoryName(filePath);
                    if (!string.IsNullOrEmpty(directory))
                    {
                        EnsureDirectoryExists(directory);
                    }
                }

                encoding = encoding ?? Encoding.UTF8;
                await File.AppendAllTextAsync(filePath, content ?? string.Empty, encoding);
            }
            catch (Exception ex)
            {
                throw new IOException($"追加文件失败: {filePath}", ex);
            }
        }

        /// <summary>
        /// 检查文件是否存在
        /// </summary>
        public static bool FileExists(string filePath)
        {
            return !string.IsNullOrWhiteSpace(filePath) && File.Exists(filePath);
        }

        /// <summary>
        /// 检查目录是否存在
        /// </summary>
        public static bool DirectoryExists(string directoryPath)
        {
            return !string.IsNullOrWhiteSpace(directoryPath) && Directory.Exists(directoryPath);
        }

        /// <summary>
        /// 安全删除文件
        /// </summary>
        public static bool TryDeleteFile(string filePath)
        {
            try
            {
                if (FileExists(filePath))
                {
                    File.Delete(filePath);
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 安全删除目录
        /// </summary>
        public static bool TryDeleteDirectory(string directoryPath, bool recursive = false)
        {
            try
            {
                if (DirectoryExists(directoryPath))
                {
                    Directory.Delete(directoryPath, recursive);
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取文件大小（字节）
        /// </summary>
        public static long GetFileSize(string filePath)
        {
            if (!FileExists(filePath))
                throw new FileNotFoundException($"文件不存在: {filePath}");

            return new FileInfo(filePath).Length;
        }

        /// <summary>
        /// 获取文件最后修改时间
        /// </summary>
        public static DateTime GetLastWriteTime(string filePath)
        {
            if (!FileExists(filePath))
                throw new FileNotFoundException($"文件不存在: {filePath}");

            return File.GetLastWriteTime(filePath);
        }

        /// <summary>
        /// 复制文件
        /// </summary>
        public static void CopyFile(string sourceFilePath, string destinationFilePath, bool overwrite = false, bool createDirectory = true)
        {
            if (string.IsNullOrWhiteSpace(sourceFilePath))
                throw new ArgumentException("源文件路径不能为空", nameof(sourceFilePath));

            if (string.IsNullOrWhiteSpace(destinationFilePath))
                throw new ArgumentException("目标文件路径不能为空", nameof(destinationFilePath));

            if (!FileExists(sourceFilePath))
                throw new FileNotFoundException($"源文件不存在: {sourceFilePath}");

            try
            {
                if (createDirectory)
                {
                    var directory = Path.GetDirectoryName(destinationFilePath);
                    if (!string.IsNullOrEmpty(directory))
                    {
                        EnsureDirectoryExists(directory);
                    }
                }

                File.Copy(sourceFilePath, destinationFilePath, overwrite);
            }
            catch (Exception ex)
            {
                throw new IOException($"复制文件失败: {sourceFilePath} -> {destinationFilePath}", ex);
            }
        }

        /// <summary>
        /// 移动文件
        /// </summary>
        public static void MoveFile(string sourceFilePath, string destinationFilePath, bool createDirectory = true)
        {
            if (string.IsNullOrWhiteSpace(sourceFilePath))
                throw new ArgumentException("源文件路径不能为空", nameof(sourceFilePath));

            if (string.IsNullOrWhiteSpace(destinationFilePath))
                throw new ArgumentException("目标文件路径不能为空", nameof(destinationFilePath));

            if (!FileExists(sourceFilePath))
                throw new FileNotFoundException($"源文件不存在: {sourceFilePath}");

            try
            {
                if (createDirectory)
                {
                    var directory = Path.GetDirectoryName(destinationFilePath);
                    if (!string.IsNullOrEmpty(directory))
                    {
                        EnsureDirectoryExists(directory);
                    }
                }

                File.Move(sourceFilePath, destinationFilePath);
            }
            catch (Exception ex)
            {
                throw new IOException($"移动文件失败: {sourceFilePath} -> {destinationFilePath}", ex);
            }
        }

        /// <summary>
        /// 获取安全的文件名（移除非法字符）
        /// </summary>
        public static string GetSafeFileName(string fileName)
        {
            if (string.IsNullOrWhiteSpace(fileName))
                return "unnamed";

            var invalidChars = Path.GetInvalidFileNameChars();
            var safeFileName = fileName;

            foreach (var invalidChar in invalidChars)
            {
                safeFileName = safeFileName.Replace(invalidChar, '_');
            }

            return safeFileName;
        }

        /// <summary>
        /// 获取唯一的文件名（如果文件已存在，添加数字后缀）
        /// </summary>
        public static string GetUniqueFileName(string filePath)
        {
            if (!FileExists(filePath))
                return filePath;

            var directory = Path.GetDirectoryName(filePath);
            var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(filePath);
            var extension = Path.GetExtension(filePath);

            var counter = 1;
            string uniqueFilePath;

            do
            {
                var uniqueFileName = $"{fileNameWithoutExtension}_{counter}{extension}";
                uniqueFilePath = Path.Combine(directory ?? string.Empty, uniqueFileName);
                counter++;
            }
            while (FileExists(uniqueFilePath));

            return uniqueFilePath;
        }
    }
}