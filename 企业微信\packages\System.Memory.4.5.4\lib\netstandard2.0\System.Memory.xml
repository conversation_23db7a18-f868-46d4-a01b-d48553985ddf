﻿<?xml version="1.0" encoding="utf-8"?><doc>
  <assembly>
    <name>System.Memory</name>
  </assembly>
  <members>
    <member name="T:System.Span`1">
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:System.Span`1.#ctor(`0[])">
      <param name="array"></param>
    </member>
    <member name="M:System.Span`1.#ctor(System.Void*,System.Int32)">
      <param name="pointer"></param>
      <param name="length"></param>
    </member>
    <member name="M:System.Span`1.#ctor(`0[],System.Int32)">
      <param name="array"></param>
      <param name="start"></param>
    </member>
    <member name="M:System.Span`1.#ctor(`0[],System.Int32,System.Int32)">
      <param name="array"></param>
      <param name="start"></param>
      <param name="length"></param>
    </member>
    <member name="M:System.Span`1.Clear">
      
    </member>
    <member name="M:System.Span`1.CopyTo(System.Span{`0})">
      <param name="destination"></param>
    </member>
    <member name="M:System.Span`1.DangerousCreate(System.Object,`0@,System.Int32)">
      <param name="obj"></param>
      <param name="objectData"></param>
      <param name="length"></param>
      <returns></returns>
    </member>
    <member name="M:System.Span`1.DangerousGetPinnableReference">
      <returns></returns>
    </member>
    <member name="P:System.Span`1.Empty">
      <returns></returns>
    </member>
    <member name="M:System.Span`1.Equals(System.Object)">
      <param name="obj"></param>
      <returns></returns>
    </member>
    <member name="M:System.Span`1.Fill(`0)">
      <param name="value"></param>
    </member>
    <member name="M:System.Span`1.GetHashCode">
      <returns></returns>
    </member>
    <member name="P:System.Span`1.IsEmpty">
      <returns></returns>
    </member>
    <member name="P:System.Span`1.Item(System.Int32)">
      <param name="index"></param>
      <returns></returns>
    </member>
    <member name="P:System.Span`1.Length">
      <returns></returns>
    </member>
    <member name="M:System.Span`1.op_Equality(System.Span{`0},System.Span{`0})">
      <param name="left"></param>
      <param name="right"></param>
      <returns></returns>
    </member>
    <member name="M:System.Span`1.op_Implicit(System.ArraySegment{T})~System.Span{T}">
      <param name="arraySegment"></param>
      <returns></returns>
    </member>
    <member name="M:System.Span`1.op_Implicit(System.Span{T})~System.ReadOnlySpan{T}">
      <param name="span"></param>
      <returns></returns>
    </member>
    <member name="M:System.Span`1.op_Implicit(T[])~System.Span{T}">
      <param name="array"></param>
      <returns></returns>
    </member>
    <member name="M:System.Span`1.op_Inequality(System.Span{`0},System.Span{`0})">
      <param name="left"></param>
      <param name="right"></param>
      <returns></returns>
    </member>
    <member name="M:System.Span`1.Slice(System.Int32)">
      <param name="start"></param>
      <returns></returns>
    </member>
    <member name="M:System.Span`1.Slice(System.Int32,System.Int32)">
      <param name="start"></param>
      <param name="length"></param>
      <returns></returns>
    </member>
    <member name="M:System.Span`1.ToArray">
      <returns></returns>
    </member>
    <member name="M:System.Span`1.TryCopyTo(System.Span{`0})">
      <param name="destination"></param>
      <returns></returns>
    </member>
    <member name="T:System.SpanExtensions">
      
    </member>
    <member name="M:System.SpanExtensions.AsBytes``1(System.ReadOnlySpan{``0})">
      <param name="source"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:System.SpanExtensions.AsBytes``1(System.Span{``0})">
      <param name="source"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:System.SpanExtensions.AsSpan(System.String)">
      <param name="text"></param>
      <returns></returns>
    </member>
    <member name="M:System.SpanExtensions.AsSpan``1(System.ArraySegment{``0})">
      <param name="arraySegment"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:System.SpanExtensions.AsSpan``1(``0[])">
      <param name="array"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:System.SpanExtensions.CopyTo``1(``0[],System.Span{``0})">
      <param name="array"></param>
      <param name="destination"></param>
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:System.SpanExtensions.IndexOf(System.Span{System.Byte},System.ReadOnlySpan{System.Byte})">
      <param name="span"></param>
      <param name="value"></param>
      <returns></returns>
    </member>
    <member name="M:System.SpanExtensions.IndexOf(System.Span{System.Byte},System.Byte)">
      <param name="span"></param>
      <param name="value"></param>
      <returns></returns>
    </member>
    <member name="M:System.SpanExtensions.IndexOf(System.ReadOnlySpan{System.Byte},System.Byte)">
      <param name="span"></param>
      <param name="value"></param>
      <returns></returns>
    </member>
    <member name="M:System.SpanExtensions.IndexOf(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte})">
      <param name="span"></param>
      <param name="value"></param>
      <returns></returns>
    </member>
    <member name="M:System.SpanExtensions.IndexOf``1(System.ReadOnlySpan{``0},System.ReadOnlySpan{``0})">
      <param name="span"></param>
      <param name="value"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:System.SpanExtensions.IndexOf``1(System.ReadOnlySpan{``0},``0)">
      <param name="span"></param>
      <param name="value"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:System.SpanExtensions.IndexOf``1(System.Span{``0},System.ReadOnlySpan{``0})">
      <param name="span"></param>
      <param name="value"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:System.SpanExtensions.IndexOf``1(System.Span{``0},``0)">
      <param name="span"></param>
      <param name="value"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:System.SpanExtensions.IndexOfAny(System.ReadOnlySpan{System.Byte},System.Byte,System.Byte,System.Byte)">
      <param name="span"></param>
      <param name="value0"></param>
      <param name="value1"></param>
      <param name="value2"></param>
      <returns></returns>
    </member>
    <member name="M:System.SpanExtensions.IndexOfAny(System.Span{System.Byte},System.Byte,System.Byte,System.Byte)">
      <param name="span"></param>
      <param name="value0"></param>
      <param name="value1"></param>
      <param name="value2"></param>
      <returns></returns>
    </member>
    <member name="M:System.SpanExtensions.IndexOfAny(System.Span{System.Byte},System.Byte,System.Byte)">
      <param name="span"></param>
      <param name="value0"></param>
      <param name="value1"></param>
      <returns></returns>
    </member>
    <member name="M:System.SpanExtensions.IndexOfAny(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte})">
      <param name="span"></param>
      <param name="values"></param>
      <returns></returns>
    </member>
    <member name="M:System.SpanExtensions.IndexOfAny(System.Span{System.Byte},System.ReadOnlySpan{System.Byte})">
      <param name="span"></param>
      <param name="values"></param>
      <returns></returns>
    </member>
    <member name="M:System.SpanExtensions.IndexOfAny(System.ReadOnlySpan{System.Byte},System.Byte,System.Byte)">
      <param name="span"></param>
      <param name="value0"></param>
      <param name="value1"></param>
      <returns></returns>
    </member>
    <member name="M:System.SpanExtensions.NonPortableCast``2(System.ReadOnlySpan{``0})">
      <param name="source"></param>
      <typeparam name="TFrom"></typeparam>
      <typeparam name="TTo"></typeparam>
      <returns></returns>
    </member>
    <member name="M:System.SpanExtensions.NonPortableCast``2(System.Span{``0})">
      <param name="source"></param>
      <typeparam name="TFrom"></typeparam>
      <typeparam name="TTo"></typeparam>
      <returns></returns>
    </member>
    <member name="M:System.SpanExtensions.SequenceEqual(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte})">
      <param name="first"></param>
      <param name="second"></param>
      <returns></returns>
    </member>
    <member name="M:System.SpanExtensions.SequenceEqual(System.Span{System.Byte},System.ReadOnlySpan{System.Byte})">
      <param name="first"></param>
      <param name="second"></param>
      <returns></returns>
    </member>
    <member name="M:System.SpanExtensions.SequenceEqual``1(System.ReadOnlySpan{``0},System.ReadOnlySpan{``0})">
      <param name="first"></param>
      <param name="second"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:System.SpanExtensions.SequenceEqual``1(System.Span{``0},System.ReadOnlySpan{``0})">
      <param name="first"></param>
      <param name="second"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:System.SpanExtensions.StartsWith(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte})">
      <param name="span"></param>
      <param name="value"></param>
      <returns></returns>
    </member>
    <member name="M:System.SpanExtensions.StartsWith(System.Span{System.Byte},System.ReadOnlySpan{System.Byte})">
      <param name="span"></param>
      <param name="value"></param>
      <returns></returns>
    </member>
    <member name="M:System.SpanExtensions.StartsWith``1(System.ReadOnlySpan{``0},System.ReadOnlySpan{``0})">
      <param name="span"></param>
      <param name="value"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="M:System.SpanExtensions.StartsWith``1(System.Span{``0},System.ReadOnlySpan{``0})">
      <param name="span"></param>
      <param name="value"></param>
      <typeparam name="T"></typeparam>
      <returns></returns>
    </member>
    <member name="T:System.ReadOnlySpan`1">
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:System.ReadOnlySpan`1.#ctor(`0[])">
      <param name="array"></param>
    </member>
    <member name="M:System.ReadOnlySpan`1.#ctor(System.Void*,System.Int32)">
      <param name="pointer"></param>
      <param name="length"></param>
    </member>
    <member name="M:System.ReadOnlySpan`1.#ctor(`0[],System.Int32)">
      <param name="array"></param>
      <param name="start"></param>
    </member>
    <member name="M:System.ReadOnlySpan`1.#ctor(`0[],System.Int32,System.Int32)">
      <param name="array"></param>
      <param name="start"></param>
      <param name="length"></param>
    </member>
    <member name="M:System.ReadOnlySpan`1.CopyTo(System.Span{`0})">
      <param name="destination"></param>
    </member>
    <member name="M:System.ReadOnlySpan`1.DangerousCreate(System.Object,`0@,System.Int32)">
      <param name="obj"></param>
      <param name="objectData"></param>
      <param name="length"></param>
      <returns></returns>
    </member>
    <member name="M:System.ReadOnlySpan`1.DangerousGetPinnableReference">
      <returns></returns>
    </member>
    <member name="P:System.ReadOnlySpan`1.Empty">
      <returns></returns>
    </member>
    <member name="M:System.ReadOnlySpan`1.Equals(System.Object)">
      <param name="obj"></param>
      <returns></returns>
    </member>
    <member name="M:System.ReadOnlySpan`1.GetHashCode">
      <returns></returns>
    </member>
    <member name="P:System.ReadOnlySpan`1.IsEmpty">
      <returns></returns>
    </member>
    <member name="P:System.ReadOnlySpan`1.Item(System.Int32)">
      <param name="index"></param>
      <returns></returns>
    </member>
    <member name="P:System.ReadOnlySpan`1.Length">
      <returns></returns>
    </member>
    <member name="M:System.ReadOnlySpan`1.op_Equality(System.ReadOnlySpan{`0},System.ReadOnlySpan{`0})">
      <param name="left"></param>
      <param name="right"></param>
      <returns></returns>
    </member>
    <member name="M:System.ReadOnlySpan`1.op_Implicit(System.ArraySegment{T})~System.ReadOnlySpan{T}">
      <param name="arraySegment"></param>
      <returns></returns>
    </member>
    <member name="M:System.ReadOnlySpan`1.op_Implicit(T[])~System.ReadOnlySpan{T}">
      <param name="array"></param>
      <returns></returns>
    </member>
    <member name="M:System.ReadOnlySpan`1.op_Inequality(System.ReadOnlySpan{`0},System.ReadOnlySpan{`0})">
      <param name="left"></param>
      <param name="right"></param>
      <returns></returns>
    </member>
    <member name="M:System.ReadOnlySpan`1.Slice(System.Int32)">
      <param name="start"></param>
      <returns></returns>
    </member>
    <member name="M:System.ReadOnlySpan`1.Slice(System.Int32,System.Int32)">
      <param name="start"></param>
      <param name="length"></param>
      <returns></returns>
    </member>
    <member name="M:System.ReadOnlySpan`1.ToArray">
      <returns></returns>
    </member>
    <member name="M:System.ReadOnlySpan`1.TryCopyTo(System.Span{`0})">
      <param name="destination"></param>
      <returns></returns>
    </member>
  </members>
</doc>