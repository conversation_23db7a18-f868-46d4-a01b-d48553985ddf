#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业微信自动回复GUI主程序
现代化图形用户界面
"""

import sys
import os
import json
import subprocess
import threading
import time
from pathlib import Path

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QFrame, QScrollArea, QSystemTrayIcon, QMenu, QAction,
    QMessageBox
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QIcon

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from gui.config_manager import ConfigManager
from gui.service_controller import ServiceController
from gui.widgets.modern_widgets import ModernButton, StatusIndicator
from gui.widgets.status_monitor import StatusMonitorCard
from gui.widgets.reply_config import ReplyConfigCard
from gui.widgets.filter_config import FilterConfigCard
from gui.styles.theme import ModernTheme


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.config_manager = ConfigManager()
        self.service_controller = ServiceController()
        self.theme = ModernTheme()
        
        self.init_ui()
        self.setup_connections()
        self.setup_system_tray()
        self.load_config()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("企业微信自动回复助手")
        self.setMinimumSize(800, 700)
        self.resize(900, 800)

        # 设置应用图标（如果图标文件存在）
        try:
            self.setWindowIcon(QIcon("gui/assets/icon.png"))
        except:
            pass  # 忽略图标加载错误

        # 应用主题样式
        self.setStyleSheet(self.theme.get_main_style())

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # 创建标题栏
        self.create_title_bar(main_layout)

        # 创建树形卡片布局
        self.create_card_layout(main_layout)
        
    def create_title_bar(self, parent_layout):
        """创建标题栏"""
        title_frame = QFrame()
        title_frame.setObjectName("titleFrame")
        title_layout = QHBoxLayout(title_frame)

        # 应用标题
        title_label = QLabel("企业微信自动回复助手")
        title_label.setObjectName("titleLabel")
        title_label.setFont(QFont("Microsoft YaHei", 18, QFont.Bold))

        # 服务状态指示器和控制按钮
        self.status_indicator = StatusIndicator()
        self.status_label = QLabel("服务已停止")
        self.status_label.setObjectName("statusLabel")

        # 控制按钮
        self.start_button = ModernButton("启动服务", "primary")
        self.stop_button = ModernButton("停止服务", "danger")
        self.stop_button.setEnabled(False)

        title_layout.addWidget(title_label)
        title_layout.addStretch()
        title_layout.addWidget(self.status_indicator)
        title_layout.addWidget(self.status_label)
        title_layout.addWidget(self.start_button)
        title_layout.addWidget(self.stop_button)

        parent_layout.addWidget(title_frame)
        
    def create_card_layout(self, parent_layout):
        """创建树形卡片布局"""
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # 创建滚动内容容器
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setContentsMargins(0, 0, 0, 0)
        scroll_layout.setSpacing(20)

        # 1. 运行状态监控卡片
        self.status_monitor_card = StatusMonitorCard()
        scroll_layout.addWidget(self.status_monitor_card)

        # 2. 自定义回复配置卡片
        self.reply_config_card = ReplyConfigCard()
        scroll_layout.addWidget(self.reply_config_card)

        # 3. 消息过滤配置卡片
        self.filter_config_card = FilterConfigCard()
        scroll_layout.addWidget(self.filter_config_card)

        # 添加弹性空间
        scroll_layout.addStretch()

        # 设置滚动区域
        scroll_area.setWidget(scroll_widget)
        parent_layout.addWidget(scroll_area)
        

        

        
    def setup_connections(self):
        """设置信号连接"""
        self.start_button.clicked.connect(self.start_service)
        self.stop_button.clicked.connect(self.stop_service)

        # 服务控制器信号
        self.service_controller.status_changed.connect(self.on_service_status_changed)
        self.service_controller.user_info_updated.connect(self.on_user_info_updated)

        # 卡片信号连接
        if hasattr(self, 'reply_config_card'):
            self.reply_config_card.content_changed.connect(self.on_reply_content_changed)
        
    def setup_system_tray(self):
        """设置系统托盘"""
        if QSystemTrayIcon.isSystemTrayAvailable():
            self.tray_icon = QSystemTrayIcon(self)
            try:
                self.tray_icon.setIcon(QIcon("gui/assets/icon.png"))
            except:
                pass  # 忽略图标加载错误
            
            # 创建托盘菜单
            tray_menu = QMenu()
            show_action = QAction("显示主窗口", self)
            show_action.triggered.connect(self.show)
            quit_action = QAction("退出", self)
            quit_action.triggered.connect(self.close)
            
            tray_menu.addAction(show_action)
            tray_menu.addSeparator()
            tray_menu.addAction(quit_action)
            
            self.tray_icon.setContextMenu(tray_menu)
            self.tray_icon.show()
            
    def load_config(self):
        """加载配置"""
        # 配置加载由各个卡片组件自己处理
        pass

    def on_reply_content_changed(self, content: str):
        """回复内容改变事件"""
        # 可以在这里添加实时保存或其他逻辑
        pass
            
    def start_service(self):
        """启动服务"""
        self.service_controller.start_service()
        
    def stop_service(self):
        """停止服务"""
        self.service_controller.stop_service()
        
    def on_service_status_changed(self, is_running):
        """服务状态改变"""
        if is_running:
            self.status_indicator.set_status("running")
            self.status_label.setText("服务运行中")
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            # 开始状态监控
            self.status_monitor_card.start_monitoring()
        else:
            self.status_indicator.set_status("stopped")
            self.status_label.setText("服务已停止")
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            # 停止状态监控
            self.status_monitor_card.stop_monitoring()

    def on_user_info_updated(self, user_info):
        """用户信息更新"""
        # 用户信息更新可以在状态监控卡片中显示
        pass
            
    def closeEvent(self, event):
        """关闭事件"""
        if self.tray_icon and self.tray_icon.isVisible():
            self.hide()
            event.ignore()
        else:
            self.service_controller.stop_service()
            event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("企业微信自动回复助手")
    app.setApplicationVersion("1.0.0")
    
    # 设置应用图标
    try:
        app.setWindowIcon(QIcon("gui/assets/icon.png"))
    except:
        pass  # 忽略图标加载错误
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
