using System;

namespace 企业微信.Models
{
    /// <summary>
    /// 配置管理器接口
    /// </summary>
    public interface IConfigManager
    {
        /// <summary>
        /// 配置变更事件
        /// </summary>
        event EventHandler<ConfigChangedEventArgs> ConfigChanged;

        /// <summary>
        /// 获取应用配置
        /// </summary>
        AppConfig GetConfig();

        /// <summary>
        /// 保存配置
        /// </summary>
        bool SaveConfig(AppConfig config);

        /// <summary>
        /// 重新加载配置
        /// </summary>
        bool ReloadConfig();

        /// <summary>
        /// 获取配置文件路径
        /// </summary>
        string GetConfigFilePath();

        /// <summary>
        /// 验证配置
        /// </summary>
        ValidationResult ValidateConfig(AppConfig config = null);

        /// <summary>
        /// 重置为默认配置
        /// </summary>
        AppConfig ResetToDefault();
    }



    /// <summary>
    /// 验证结果
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public string[] Errors { get; set; } = new string[0];
        public string[] Warnings { get; set; } = new string[0];

        public ValidationResult(bool isValid = true)
        {
            IsValid = isValid;
        }
    }
}