﻿using AntdUI;

namespace 企业微信
{
    partial class Form1
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            
            // 主容器
            this.mainPanel = new AntdUI.Panel();
            this.headerPanel = new AntdUI.Panel();
            this.contentPanel = new AntdUI.Panel();
            
            // 标题栏
            this.lblTitle = new AntdUI.Label();
            this.lblServiceStatus = new AntdUI.Label();
            this.btnStartService = new AntdUI.Button();
            this.btnStopService = new AntdUI.Button();
            this.btnRestartService = new AntdUI.Button();
            
            // 选项卡控件
            this.tabControl = new AntdUI.Tabs();
            this.tabPageConfig = new AntdUI.TabPage();
            this.tabPageMonitor = new AntdUI.TabPage();
            this.tabPageLogs = new AntdUI.TabPage();
            this.tabPageMessages = new AntdUI.TabPage();
            
            // 配置页面控件
            this.configPanel = new AntdUI.Panel();
            this.difyConfigGroup = new AntdUI.Panel();
            this.wechatConfigGroup = new AntdUI.Panel();
            this.appConfigGroup = new AntdUI.Panel();
            
            // Dify配置
            this.lblDifyTitle = new AntdUI.Label();
            this.lblDifyApiUrl = new AntdUI.Label();
            this.txtDifyApiUrl = new AntdUI.Input();
            this.lblDifyApiKey = new AntdUI.Label();
            this.txtDifyApiKey = new AntdUI.Input();
            this.lblDifyTimeout = new AntdUI.Label();
            this.numDifyTimeout = new AntdUI.InputNumber();
            this.btnTestDify = new AntdUI.Button();
            this.lblDifyStatus = new AntdUI.Label();
            
            // 企业微信配置
            this.lblWeChatTitle = new AntdUI.Label();
            this.lblWeChatCorpId = new AntdUI.Label();
            this.txtWeChatCorpId = new AntdUI.Input();
            this.lblWeChatCorpSecret = new AntdUI.Label();
            this.txtWeChatCorpSecret = new AntdUI.Input();
            this.lblWeChatAgentId = new AntdUI.Label();
            this.txtWeChatAgentId = new AntdUI.Input();
            this.btnTestWeChat = new AntdUI.Button();
            this.lblWeChatStatus = new AntdUI.Label();
            
            // 应用配置
            this.lblAppTitle = new AntdUI.Label();
            this.chkAutoStart = new AntdUI.Checkbox();
            this.chkMinimizeToTray = new AntdUI.Checkbox();
            this.lblLogRetentionDays = new AntdUI.Label();
            this.numLogRetentionDays = new AntdUI.InputNumber();
            this.btnSaveConfig = new AntdUI.Button();
            
            // 监控页面控件
            this.monitorPanel = new AntdUI.Panel();
            this.statsPanel = new AntdUI.Panel();
            this.lblStatsTitle = new AntdUI.Label();
            this.lblTotalMessagesLabel = new AntdUI.Label();
            this.lblTotalMessages = new AntdUI.Label();
            this.lblSuccessMessagesLabel = new AntdUI.Label();
            this.lblSuccessMessages = new AntdUI.Label();
            this.lblFailedMessagesLabel = new AntdUI.Label();
            this.lblFailedMessages = new AntdUI.Label();
            this.lblUptimeLabel = new AntdUI.Label();
            this.lblUptime = new AntdUI.Label();
            
            // 日志页面控件
            this.logsPanel = new AntdUI.Panel();
            this.txtLogs = new AntdUI.Input();
            this.btnClearLogs = new AntdUI.Button();
            
            // 消息页面控件
            this.messagesPanel = new AntdUI.Panel();
            this.listMessages = new System.Windows.Forms.ListView();
            this.colTime = new System.Windows.Forms.ColumnHeader();
            this.colSender = new System.Windows.Forms.ColumnHeader();
            this.colContent = new System.Windows.Forms.ColumnHeader();
            this.colStatus = new System.Windows.Forms.ColumnHeader();
            
            this.SuspendLayout();
            
            // 
            // mainPanel
            // 
            this.mainPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.mainPanel.Location = new System.Drawing.Point(0, 0);
            this.mainPanel.Name = "mainPanel";
            this.mainPanel.Size = new System.Drawing.Size(1000, 700);
            this.mainPanel.TabIndex = 0;
            this.mainPanel.Controls.Add(this.headerPanel);
            this.mainPanel.Controls.Add(this.contentPanel);
            
            // 
            // headerPanel
            // 
            this.headerPanel.Dock = System.Windows.Forms.DockStyle.Top;
            this.headerPanel.Height = 80;
            this.headerPanel.Location = new System.Drawing.Point(0, 0);
            this.headerPanel.Name = "headerPanel";
            this.headerPanel.Size = new System.Drawing.Size(1000, 80);
            this.headerPanel.TabIndex = 0;
            this.headerPanel.BackColor = System.Drawing.Color.FromArgb(240, 242, 245);
            this.headerPanel.Controls.Add(this.lblTitle);
            this.headerPanel.Controls.Add(this.lblServiceStatus);
            this.headerPanel.Controls.Add(this.btnStartService);
            this.headerPanel.Controls.Add(this.btnStopService);
            this.headerPanel.Controls.Add(this.btnRestartService);
            
            // 
            // lblTitle
            // 
            this.lblTitle.AutoSize = true;
            this.lblTitle.Font = new System.Drawing.Font("Microsoft YaHei UI", 16F, System.Drawing.FontStyle.Bold);
            this.lblTitle.ForeColor = System.Drawing.Color.FromArgb(51, 51, 51);
            this.lblTitle.Location = new System.Drawing.Point(20, 20);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new System.Drawing.Size(200, 30);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "企业微信 Dify 集成";
            
            // 
            // lblServiceStatus
            // 
            this.lblServiceStatus.AutoSize = true;
            this.lblServiceStatus.Font = new System.Drawing.Font("Microsoft YaHei UI", 12F);
            this.lblServiceStatus.Location = new System.Drawing.Point(250, 25);
            this.lblServiceStatus.Name = "lblServiceStatus";
            this.lblServiceStatus.Size = new System.Drawing.Size(60, 21);
            this.lblServiceStatus.TabIndex = 1;
            this.lblServiceStatus.Text = "已停止";
            this.lblServiceStatus.ForeColor = System.Drawing.Color.Red;
            
            // 
            // btnStartService
            // 
            this.btnStartService.Location = new System.Drawing.Point(700, 20);
            this.btnStartService.Name = "btnStartService";
            this.btnStartService.Size = new System.Drawing.Size(80, 35);
            this.btnStartService.TabIndex = 2;
            this.btnStartService.Text = "启动服务";
            this.btnStartService.Type = AntdUI.TTypeMini.Primary;
            this.btnStartService.Click += new System.EventHandler(this.btnStartService_Click);
            
            // 
            // btnStopService
            // 
            this.btnStopService.Location = new System.Drawing.Point(790, 20);
            this.btnStopService.Name = "btnStopService";
            this.btnStopService.Size = new System.Drawing.Size(80, 35);
            this.btnStopService.TabIndex = 3;
            this.btnStopService.Text = "停止服务";
            this.btnStopService.Type = AntdUI.TTypeMini.Default;
            this.btnStopService.Enabled = false;
            this.btnStopService.Click += new System.EventHandler(this.btnStopService_Click);
            
            // 
            // btnRestartService
            // 
            this.btnRestartService.Location = new System.Drawing.Point(880, 20);
            this.btnRestartService.Name = "btnRestartService";
            this.btnRestartService.Size = new System.Drawing.Size(80, 35);
            this.btnRestartService.TabIndex = 4;
            this.btnRestartService.Text = "重启服务";
            this.btnRestartService.Type = AntdUI.TTypeMini.Default;
            this.btnRestartService.Enabled = false;
            this.btnRestartService.Click += new System.EventHandler(this.btnRestartService_Click);
            
            // 
            // contentPanel
            // 
            this.contentPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.contentPanel.Location = new System.Drawing.Point(0, 80);
            this.contentPanel.Name = "contentPanel";
            this.contentPanel.Size = new System.Drawing.Size(1000, 620);
            this.contentPanel.TabIndex = 1;
            this.contentPanel.Controls.Add(this.tabControl);
            
            // 
            // tabControl
            // 
            this.tabControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl.Location = new System.Drawing.Point(0, 0);
            this.tabControl.Name = "tabControl";
            this.tabControl.Size = new System.Drawing.Size(1000, 620);
            this.tabControl.TabIndex = 0;
            this.tabControl.Controls.Add(this.tabPageConfig);
            this.tabControl.Controls.Add(this.tabPageMonitor);
            this.tabControl.Controls.Add(this.tabPageMessages);
            this.tabControl.Controls.Add(this.tabPageLogs);
            
            // 
            // tabPageConfig
            // 
            this.tabPageConfig.Location = new System.Drawing.Point(4, 26);
            this.tabPageConfig.Name = "tabPageConfig";
            this.tabPageConfig.Padding = new System.Windows.Forms.Padding(20);
            this.tabPageConfig.Size = new System.Drawing.Size(992, 590);
            this.tabPageConfig.TabIndex = 0;
            this.tabPageConfig.Text = "配置管理";
            // this.tabPageConfig.UseVisualStyleBackColor = true; // AntdUI TabPage不支持此属性
            this.tabPageConfig.Controls.Add(this.configPanel);
            
            // 
            // configPanel
            // 
            this.configPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.configPanel.Location = new System.Drawing.Point(20, 20);
            this.configPanel.Name = "configPanel";
            this.configPanel.Size = new System.Drawing.Size(952, 550);
            this.configPanel.TabIndex = 0;
            this.configPanel.Controls.Add(this.difyConfigGroup);
            this.configPanel.Controls.Add(this.wechatConfigGroup);
            this.configPanel.Controls.Add(this.appConfigGroup);
            
            // 
            // difyConfigGroup
            // 
            this.difyConfigGroup.Location = new System.Drawing.Point(0, 0);
            this.difyConfigGroup.Name = "difyConfigGroup";
            this.difyConfigGroup.Size = new System.Drawing.Size(450, 250);
            this.difyConfigGroup.TabIndex = 0;
            this.difyConfigGroup.BackColor = System.Drawing.Color.White;
            // this.difyConfigGroup.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle; // AntdUI GroupBox使用不同的边框设置
            this.difyConfigGroup.Controls.Add(this.lblDifyTitle);
            this.difyConfigGroup.Controls.Add(this.lblDifyApiUrl);
            this.difyConfigGroup.Controls.Add(this.txtDifyApiUrl);
            this.difyConfigGroup.Controls.Add(this.lblDifyApiKey);
            this.difyConfigGroup.Controls.Add(this.txtDifyApiKey);
            this.difyConfigGroup.Controls.Add(this.lblDifyTimeout);
            this.difyConfigGroup.Controls.Add(this.numDifyTimeout);
            this.difyConfigGroup.Controls.Add(this.btnTestDify);
            this.difyConfigGroup.Controls.Add(this.lblDifyStatus);
            
            // 
            // lblDifyTitle
            // 
            this.lblDifyTitle.AutoSize = true;
            this.lblDifyTitle.Font = new System.Drawing.Font("Microsoft YaHei UI", 12F, System.Drawing.FontStyle.Bold);
            this.lblDifyTitle.ForeColor = System.Drawing.Color.FromArgb(51, 51, 51);
            this.lblDifyTitle.Location = new System.Drawing.Point(15, 15);
            this.lblDifyTitle.Name = "lblDifyTitle";
            this.lblDifyTitle.Size = new System.Drawing.Size(90, 22);
            this.lblDifyTitle.TabIndex = 0;
            this.lblDifyTitle.Text = "Dify 配置";
            
            // 
            // lblDifyApiUrl
            // 
            this.lblDifyApiUrl.AutoSize = true;
            this.lblDifyApiUrl.Location = new System.Drawing.Point(15, 50);
            this.lblDifyApiUrl.Name = "lblDifyApiUrl";
            this.lblDifyApiUrl.Size = new System.Drawing.Size(65, 17);
            this.lblDifyApiUrl.TabIndex = 1;
            this.lblDifyApiUrl.Text = "API 地址:";
            
            // 
            // txtDifyApiUrl
            // 
            this.txtDifyApiUrl.Location = new System.Drawing.Point(100, 47);
            this.txtDifyApiUrl.Name = "txtDifyApiUrl";
            this.txtDifyApiUrl.Size = new System.Drawing.Size(320, 32);
            this.txtDifyApiUrl.TabIndex = 2;
            this.txtDifyApiUrl.PlaceholderText = "请输入 Dify API 地址";
            
            // 
            // lblDifyApiKey
            // 
            this.lblDifyApiKey.AutoSize = true;
            this.lblDifyApiKey.Location = new System.Drawing.Point(15, 90);
            this.lblDifyApiKey.Name = "lblDifyApiKey";
            this.lblDifyApiKey.Size = new System.Drawing.Size(65, 17);
            this.lblDifyApiKey.TabIndex = 3;
            this.lblDifyApiKey.Text = "API 密钥:";
            
            // 
            // txtDifyApiKey
            // 
            this.txtDifyApiKey.Location = new System.Drawing.Point(100, 87);
            this.txtDifyApiKey.Name = "txtDifyApiKey";
            this.txtDifyApiKey.Size = new System.Drawing.Size(320, 32);
            this.txtDifyApiKey.TabIndex = 4;
            this.txtDifyApiKey.PlaceholderText = "请输入 Dify API 密钥";
            this.txtDifyApiKey.UseSystemPasswordChar = true;
            
            // 
            // lblDifyTimeout
            // 
            this.lblDifyTimeout.AutoSize = true;
            this.lblDifyTimeout.Location = new System.Drawing.Point(15, 130);
            this.lblDifyTimeout.Name = "lblDifyTimeout";
            this.lblDifyTimeout.Size = new System.Drawing.Size(77, 17);
            this.lblDifyTimeout.TabIndex = 5;
            this.lblDifyTimeout.Text = "超时时间(秒):";
            
            // 
            // numDifyTimeout
            // 
            this.numDifyTimeout.Location = new System.Drawing.Point(100, 127);
            this.numDifyTimeout.Name = "numDifyTimeout";
            this.numDifyTimeout.Size = new System.Drawing.Size(100, 32);
            this.numDifyTimeout.TabIndex = 6;
            this.numDifyTimeout.Minimum = 5;
            this.numDifyTimeout.Maximum = 300;
            this.numDifyTimeout.Value = 30;
            
            // 
            // btnTestDify
            // 
            this.btnTestDify.Location = new System.Drawing.Point(15, 180);
            this.btnTestDify.Name = "btnTestDify";
            this.btnTestDify.Size = new System.Drawing.Size(100, 35);
            this.btnTestDify.TabIndex = 7;
            this.btnTestDify.Text = "测试连接";
            this.btnTestDify.Type = AntdUI.TTypeMini.Primary;
            this.btnTestDify.Click += new System.EventHandler(this.btnTestDify_Click);
            
            // 
            // lblDifyStatus
            // 
            this.lblDifyStatus.AutoSize = true;
            this.lblDifyStatus.Location = new System.Drawing.Point(130, 188);
            this.lblDifyStatus.Name = "lblDifyStatus";
            this.lblDifyStatus.Size = new System.Drawing.Size(56, 17);
            this.lblDifyStatus.TabIndex = 8;
            this.lblDifyStatus.Text = "未连接";
            this.lblDifyStatus.ForeColor = System.Drawing.Color.Gray;
            
            // 
            // wechatConfigGroup
            // 
            this.wechatConfigGroup.Location = new System.Drawing.Point(470, 0);
            this.wechatConfigGroup.Name = "wechatConfigGroup";
            this.wechatConfigGroup.Size = new System.Drawing.Size(450, 250);
            this.wechatConfigGroup.TabIndex = 1;
            this.wechatConfigGroup.BackColor = System.Drawing.Color.White;
            // this.wechatConfigGroup.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle; // AntdUI GroupBox使用不同的边框设置
            this.wechatConfigGroup.Controls.Add(this.lblWeChatTitle);
            this.wechatConfigGroup.Controls.Add(this.lblWeChatCorpId);
            this.wechatConfigGroup.Controls.Add(this.txtWeChatCorpId);
            this.wechatConfigGroup.Controls.Add(this.lblWeChatCorpSecret);
            this.wechatConfigGroup.Controls.Add(this.txtWeChatCorpSecret);
            this.wechatConfigGroup.Controls.Add(this.lblWeChatAgentId);
            this.wechatConfigGroup.Controls.Add(this.txtWeChatAgentId);
            this.wechatConfigGroup.Controls.Add(this.btnTestWeChat);
            this.wechatConfigGroup.Controls.Add(this.lblWeChatStatus);
            
            // 
            // lblWeChatTitle
            // 
            this.lblWeChatTitle.AutoSize = true;
            this.lblWeChatTitle.Font = new System.Drawing.Font("Microsoft YaHei UI", 12F, System.Drawing.FontStyle.Bold);
            this.lblWeChatTitle.ForeColor = System.Drawing.Color.FromArgb(51, 51, 51);
            this.lblWeChatTitle.Location = new System.Drawing.Point(15, 15);
            this.lblWeChatTitle.Name = "lblWeChatTitle";
            this.lblWeChatTitle.Size = new System.Drawing.Size(106, 22);
            this.lblWeChatTitle.TabIndex = 0;
            this.lblWeChatTitle.Text = "企业微信配置";
            
            // 
            // lblWeChatCorpId
            // 
            this.lblWeChatCorpId.AutoSize = true;
            this.lblWeChatCorpId.Location = new System.Drawing.Point(15, 50);
            this.lblWeChatCorpId.Name = "lblWeChatCorpId";
            this.lblWeChatCorpId.Size = new System.Drawing.Size(65, 17);
            this.lblWeChatCorpId.TabIndex = 1;
            this.lblWeChatCorpId.Text = "企业 ID:";
            
            // 
            // txtWeChatCorpId
            // 
            this.txtWeChatCorpId.Location = new System.Drawing.Point(100, 47);
            this.txtWeChatCorpId.Name = "txtWeChatCorpId";
            this.txtWeChatCorpId.Size = new System.Drawing.Size(320, 32);
            this.txtWeChatCorpId.TabIndex = 2;
            this.txtWeChatCorpId.PlaceholderText = "请输入企业微信 CorpId";
            
            // 
            // lblWeChatCorpSecret
            // 
            this.lblWeChatCorpSecret.AutoSize = true;
            this.lblWeChatCorpSecret.Location = new System.Drawing.Point(15, 90);
            this.lblWeChatCorpSecret.Name = "lblWeChatCorpSecret";
            this.lblWeChatCorpSecret.Size = new System.Drawing.Size(77, 17);
            this.lblWeChatCorpSecret.TabIndex = 3;
            this.lblWeChatCorpSecret.Text = "企业密钥:";
            
            // 
            // txtWeChatCorpSecret
            // 
            this.txtWeChatCorpSecret.Location = new System.Drawing.Point(100, 87);
            this.txtWeChatCorpSecret.Name = "txtWeChatCorpSecret";
            this.txtWeChatCorpSecret.Size = new System.Drawing.Size(320, 32);
            this.txtWeChatCorpSecret.TabIndex = 4;
            this.txtWeChatCorpSecret.PlaceholderText = "请输入企业微信 CorpSecret";
            this.txtWeChatCorpSecret.UseSystemPasswordChar = true;
            
            // 
            // lblWeChatAgentId
            // 
            this.lblWeChatAgentId.AutoSize = true;
            this.lblWeChatAgentId.Location = new System.Drawing.Point(15, 130);
            this.lblWeChatAgentId.Name = "lblWeChatAgentId";
            this.lblWeChatAgentId.Size = new System.Drawing.Size(77, 17);
            this.lblWeChatAgentId.TabIndex = 5;
            this.lblWeChatAgentId.Text = "应用 ID:";
            
            // 
            // txtWeChatAgentId
            // 
            this.txtWeChatAgentId.Location = new System.Drawing.Point(100, 127);
            this.txtWeChatAgentId.Name = "txtWeChatAgentId";
            this.txtWeChatAgentId.Size = new System.Drawing.Size(320, 32);
            this.txtWeChatAgentId.TabIndex = 6;
            this.txtWeChatAgentId.PlaceholderText = "请输入企业微信 AgentId";
            
            // 
            // btnTestWeChat
            // 
            this.btnTestWeChat.Location = new System.Drawing.Point(15, 180);
            this.btnTestWeChat.Name = "btnTestWeChat";
            this.btnTestWeChat.Size = new System.Drawing.Size(100, 35);
            this.btnTestWeChat.TabIndex = 7;
            this.btnTestWeChat.Text = "测试连接";
            this.btnTestWeChat.Type = AntdUI.TTypeMini.Primary;
            this.btnTestWeChat.Click += new System.EventHandler(this.btnTestWeChat_Click);
            
            // 
            // lblWeChatStatus
            // 
            this.lblWeChatStatus.AutoSize = true;
            this.lblWeChatStatus.Location = new System.Drawing.Point(130, 188);
            this.lblWeChatStatus.Name = "lblWeChatStatus";
            this.lblWeChatStatus.Size = new System.Drawing.Size(56, 17);
            this.lblWeChatStatus.TabIndex = 8;
            this.lblWeChatStatus.Text = "未连接";
            this.lblWeChatStatus.ForeColor = System.Drawing.Color.Gray;
            
            // 
            // appConfigGroup
            // 
            this.appConfigGroup.Location = new System.Drawing.Point(0, 270);
            this.appConfigGroup.Name = "appConfigGroup";
            this.appConfigGroup.Size = new System.Drawing.Size(920, 200);
            this.appConfigGroup.TabIndex = 2;
            this.appConfigGroup.BackColor = System.Drawing.Color.White;
            // this.appConfigGroup.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle; // AntdUI GroupBox使用不同的边框设置
            this.appConfigGroup.Controls.Add(this.lblAppTitle);
            this.appConfigGroup.Controls.Add(this.chkAutoStart);
            this.appConfigGroup.Controls.Add(this.chkMinimizeToTray);
            this.appConfigGroup.Controls.Add(this.lblLogRetentionDays);
            this.appConfigGroup.Controls.Add(this.numLogRetentionDays);
            this.appConfigGroup.Controls.Add(this.btnSaveConfig);
            
            // 
            // lblAppTitle
            // 
            this.lblAppTitle.AutoSize = true;
            this.lblAppTitle.Font = new System.Drawing.Font("Microsoft YaHei UI", 12F, System.Drawing.FontStyle.Bold);
            this.lblAppTitle.ForeColor = System.Drawing.Color.FromArgb(51, 51, 51);
            this.lblAppTitle.Location = new System.Drawing.Point(15, 15);
            this.lblAppTitle.Name = "lblAppTitle";
            this.lblAppTitle.Size = new System.Drawing.Size(90, 22);
            this.lblAppTitle.TabIndex = 0;
            this.lblAppTitle.Text = "应用设置";
            
            // 
            // chkAutoStart
            // 
            this.chkAutoStart.AutoSize = true;
            this.chkAutoStart.Location = new System.Drawing.Point(15, 50);
            this.chkAutoStart.Name = "chkAutoStart";
            this.chkAutoStart.Size = new System.Drawing.Size(87, 21);
            this.chkAutoStart.TabIndex = 1;
            this.chkAutoStart.Text = "开机自启动";
            // this.chkAutoStart.UseVisualStyleBackColor = true; // AntdUI Checkbox不支持此属性
            
            // 
            // chkMinimizeToTray
            // 
            this.chkMinimizeToTray.AutoSize = true;
            this.chkMinimizeToTray.Location = new System.Drawing.Point(15, 80);
            this.chkMinimizeToTray.Name = "chkMinimizeToTray";
            this.chkMinimizeToTray.Size = new System.Drawing.Size(111, 21);
            this.chkMinimizeToTray.TabIndex = 2;
            this.chkMinimizeToTray.Text = "最小化到托盘";
            // this.chkMinimizeToTray.UseVisualStyleBackColor = true; // AntdUI Checkbox不支持此属性
            
            // 
            // lblLogRetentionDays
            // 
            this.lblLogRetentionDays.AutoSize = true;
            this.lblLogRetentionDays.Location = new System.Drawing.Point(15, 115);
            this.lblLogRetentionDays.Name = "lblLogRetentionDays";
            this.lblLogRetentionDays.Size = new System.Drawing.Size(101, 17);
            this.lblLogRetentionDays.TabIndex = 3;
            this.lblLogRetentionDays.Text = "日志保留天数:";
            
            // 
            // numLogRetentionDays
            // 
            this.numLogRetentionDays.Location = new System.Drawing.Point(130, 112);
            this.numLogRetentionDays.Name = "numLogRetentionDays";
            this.numLogRetentionDays.Size = new System.Drawing.Size(100, 32);
            this.numLogRetentionDays.TabIndex = 4;
            this.numLogRetentionDays.Minimum = 1;
            this.numLogRetentionDays.Maximum = 365;
            this.numLogRetentionDays.Value = 30;
            
            // 
            // btnSaveConfig
            // 
            this.btnSaveConfig.Location = new System.Drawing.Point(15, 155);
            this.btnSaveConfig.Name = "btnSaveConfig";
            this.btnSaveConfig.Size = new System.Drawing.Size(100, 35);
            this.btnSaveConfig.TabIndex = 5;
            this.btnSaveConfig.Text = "保存配置";
            this.btnSaveConfig.Type = AntdUI.TTypeMini.Primary;
            this.btnSaveConfig.Click += new System.EventHandler(this.btnSaveConfig_Click);
            
            // 
            // tabPageMonitor
            // 
            this.tabPageMonitor.Location = new System.Drawing.Point(4, 26);
            this.tabPageMonitor.Name = "tabPageMonitor";
            this.tabPageMonitor.Padding = new System.Windows.Forms.Padding(20);
            this.tabPageMonitor.Size = new System.Drawing.Size(992, 590);
            this.tabPageMonitor.TabIndex = 1;
            this.tabPageMonitor.Text = "运行监控";
            // this.tabPageMonitor.UseVisualStyleBackColor = true; // AntdUI TabPage不支持此属性
            this.tabPageMonitor.Controls.Add(this.monitorPanel);
            
            // 
            // monitorPanel
            // 
            this.monitorPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.monitorPanel.Location = new System.Drawing.Point(20, 20);
            this.monitorPanel.Name = "monitorPanel";
            this.monitorPanel.Size = new System.Drawing.Size(952, 550);
            this.monitorPanel.TabIndex = 0;
            this.monitorPanel.Controls.Add(this.statsPanel);
            
            // 
            // statsPanel
            // 
            this.statsPanel.Location = new System.Drawing.Point(0, 0);
            this.statsPanel.Name = "statsPanel";
            this.statsPanel.Size = new System.Drawing.Size(500, 300);
            this.statsPanel.TabIndex = 0;
            this.statsPanel.BackColor = System.Drawing.Color.White;
            // this.statsPanel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle; // AntdUI Panel使用不同的边框设置
            this.statsPanel.Controls.Add(this.lblStatsTitle);
            this.statsPanel.Controls.Add(this.lblTotalMessagesLabel);
            this.statsPanel.Controls.Add(this.lblTotalMessages);
            this.statsPanel.Controls.Add(this.lblSuccessMessagesLabel);
            this.statsPanel.Controls.Add(this.lblSuccessMessages);
            this.statsPanel.Controls.Add(this.lblFailedMessagesLabel);
            this.statsPanel.Controls.Add(this.lblFailedMessages);
            this.statsPanel.Controls.Add(this.lblUptimeLabel);
            this.statsPanel.Controls.Add(this.lblUptime);
            
            // 
            // lblStatsTitle
            // 
            this.lblStatsTitle.AutoSize = true;
            this.lblStatsTitle.Font = new System.Drawing.Font("Microsoft YaHei UI", 12F, System.Drawing.FontStyle.Bold);
            this.lblStatsTitle.ForeColor = System.Drawing.Color.FromArgb(51, 51, 51);
            this.lblStatsTitle.Location = new System.Drawing.Point(15, 15);
            this.lblStatsTitle.Name = "lblStatsTitle";
            this.lblStatsTitle.Size = new System.Drawing.Size(90, 22);
            this.lblStatsTitle.TabIndex = 0;
            this.lblStatsTitle.Text = "运行统计";
            
            // 
            // lblTotalMessagesLabel
            // 
            this.lblTotalMessagesLabel.AutoSize = true;
            this.lblTotalMessagesLabel.Location = new System.Drawing.Point(15, 60);
            this.lblTotalMessagesLabel.Name = "lblTotalMessagesLabel";
            this.lblTotalMessagesLabel.Size = new System.Drawing.Size(77, 17);
            this.lblTotalMessagesLabel.TabIndex = 1;
            this.lblTotalMessagesLabel.Text = "总消息数:";
            
            // 
            // lblTotalMessages
            // 
            this.lblTotalMessages.AutoSize = true;
            this.lblTotalMessages.Font = new System.Drawing.Font("Microsoft YaHei UI", 12F, System.Drawing.FontStyle.Bold);
            this.lblTotalMessages.ForeColor = System.Drawing.Color.FromArgb(24, 144, 255);
            this.lblTotalMessages.Location = new System.Drawing.Point(100, 58);
            this.lblTotalMessages.Name = "lblTotalMessages";
            this.lblTotalMessages.Size = new System.Drawing.Size(19, 22);
            this.lblTotalMessages.TabIndex = 2;
            this.lblTotalMessages.Text = "0";
            
            // 
            // lblSuccessMessagesLabel
            // 
            this.lblSuccessMessagesLabel.AutoSize = true;
            this.lblSuccessMessagesLabel.Location = new System.Drawing.Point(15, 100);
            this.lblSuccessMessagesLabel.Name = "lblSuccessMessagesLabel";
            this.lblSuccessMessagesLabel.Size = new System.Drawing.Size(77, 17);
            this.lblSuccessMessagesLabel.TabIndex = 3;
            this.lblSuccessMessagesLabel.Text = "成功消息:";
            
            // 
            // lblSuccessMessages
            // 
            this.lblSuccessMessages.AutoSize = true;
            this.lblSuccessMessages.Font = new System.Drawing.Font("Microsoft YaHei UI", 12F, System.Drawing.FontStyle.Bold);
            this.lblSuccessMessages.ForeColor = System.Drawing.Color.FromArgb(82, 196, 26);
            this.lblSuccessMessages.Location = new System.Drawing.Point(100, 98);
            this.lblSuccessMessages.Name = "lblSuccessMessages";
            this.lblSuccessMessages.Size = new System.Drawing.Size(19, 22);
            this.lblSuccessMessages.TabIndex = 4;
            this.lblSuccessMessages.Text = "0";
            
            // 
            // lblFailedMessagesLabel
            // 
            this.lblFailedMessagesLabel.AutoSize = true;
            this.lblFailedMessagesLabel.Location = new System.Drawing.Point(15, 140);
            this.lblFailedMessagesLabel.Name = "lblFailedMessagesLabel";
            this.lblFailedMessagesLabel.Size = new System.Drawing.Size(77, 17);
            this.lblFailedMessagesLabel.TabIndex = 5;
            this.lblFailedMessagesLabel.Text = "失败消息:";
            
            // 
            // lblFailedMessages
            // 
            this.lblFailedMessages.AutoSize = true;
            this.lblFailedMessages.Font = new System.Drawing.Font("Microsoft YaHei UI", 12F, System.Drawing.FontStyle.Bold);
            this.lblFailedMessages.ForeColor = System.Drawing.Color.FromArgb(255, 77, 79);
            this.lblFailedMessages.Location = new System.Drawing.Point(100, 138);
            this.lblFailedMessages.Name = "lblFailedMessages";
            this.lblFailedMessages.Size = new System.Drawing.Size(19, 22);
            this.lblFailedMessages.TabIndex = 6;
            this.lblFailedMessages.Text = "0";
            
            // 
            // lblUptimeLabel
            // 
            this.lblUptimeLabel.AutoSize = true;
            this.lblUptimeLabel.Location = new System.Drawing.Point(15, 180);
            this.lblUptimeLabel.Name = "lblUptimeLabel";
            this.lblUptimeLabel.Size = new System.Drawing.Size(77, 17);
            this.lblUptimeLabel.TabIndex = 7;
            this.lblUptimeLabel.Text = "运行时间:";
            
            // 
            // lblUptime
            // 
            this.lblUptime.AutoSize = true;
            this.lblUptime.Font = new System.Drawing.Font("Microsoft YaHei UI", 12F, System.Drawing.FontStyle.Bold);
            this.lblUptime.ForeColor = System.Drawing.Color.FromArgb(51, 51, 51);
            this.lblUptime.Location = new System.Drawing.Point(100, 178);
            this.lblUptime.Name = "lblUptime";
            this.lblUptime.Size = new System.Drawing.Size(76, 22);
            this.lblUptime.TabIndex = 8;
            this.lblUptime.Text = "00:00:00";
            
            // 
            // tabPageMessages
            // 
            this.tabPageMessages.Location = new System.Drawing.Point(4, 26);
            this.tabPageMessages.Name = "tabPageMessages";
            this.tabPageMessages.Padding = new System.Windows.Forms.Padding(20);
            this.tabPageMessages.Size = new System.Drawing.Size(992, 590);
            this.tabPageMessages.TabIndex = 2;
            this.tabPageMessages.Text = "消息记录";
            // this.tabPageMessages.UseVisualStyleBackColor = true; // AntdUI TabPage不支持此属性
            this.tabPageMessages.Controls.Add(this.messagesPanel);
            
            // 
            // messagesPanel
            // 
            this.messagesPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.messagesPanel.Location = new System.Drawing.Point(20, 20);
            this.messagesPanel.Name = "messagesPanel";
            this.messagesPanel.Size = new System.Drawing.Size(952, 550);
            this.messagesPanel.TabIndex = 0;
            this.messagesPanel.Controls.Add(this.listMessages);
            
            // 
            // listMessages
            // 
            this.listMessages.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listMessages.FullRowSelect = true;
            this.listMessages.GridLines = true;
            this.listMessages.Location = new System.Drawing.Point(0, 0);
            this.listMessages.Name = "listMessages";
            this.listMessages.Size = new System.Drawing.Size(952, 550);
            this.listMessages.TabIndex = 0;
            this.listMessages.UseCompatibleStateImageBehavior = false;
            this.listMessages.View = System.Windows.Forms.View.Details;
            this.listMessages.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
                this.colTime,
                this.colSender,
                this.colContent,
                this.colStatus});
            
            // 
            // colTime
            // 
            this.colTime.Text = "时间";
            this.colTime.Width = 100;
            
            // 
            // colSender
            // 
            this.colSender.Text = "发送者";
            this.colSender.Width = 150;
            
            // 
            // colContent
            // 
            this.colContent.Text = "消息内容";
            this.colContent.Width = 500;
            
            // 
            // colStatus
            // 
            this.colStatus.Text = "状态";
            this.colStatus.Width = 100;
            
            // 
            // tabPageLogs
            // 
            this.tabPageLogs.Location = new System.Drawing.Point(4, 26);
            this.tabPageLogs.Name = "tabPageLogs";
            this.tabPageLogs.Padding = new System.Windows.Forms.Padding(20);
            this.tabPageLogs.Size = new System.Drawing.Size(992, 590);
            this.tabPageLogs.TabIndex = 3;
            this.tabPageLogs.Text = "系统日志";
            // this.tabPageLogs.UseVisualStyleBackColor = true; // AntdUI TabPage不支持此属性
            this.tabPageLogs.Controls.Add(this.logsPanel);
            
            // 
            // logsPanel
            // 
            this.logsPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.logsPanel.Location = new System.Drawing.Point(20, 20);
            this.logsPanel.Name = "logsPanel";
            this.logsPanel.Size = new System.Drawing.Size(952, 550);
            this.logsPanel.TabIndex = 0;
            this.logsPanel.Controls.Add(this.txtLogs);
            this.logsPanel.Controls.Add(this.btnClearLogs);
            
            // 
            // txtLogs
            // 
            this.txtLogs.Location = new System.Drawing.Point(0, 0);
            this.txtLogs.Name = "txtLogs";
            this.txtLogs.Size = new System.Drawing.Size(952, 500);
            this.txtLogs.TabIndex = 0;
            this.txtLogs.Multiline = true;
            this.txtLogs.ReadOnly = true;
            // this.txtLogs.ScrollBars = System.Windows.Forms.ScrollBars.Vertical; // AntdUI Input不支持ScrollBars属性
            this.txtLogs.Font = new System.Drawing.Font("Consolas", 9F);
            this.txtLogs.BackColor = System.Drawing.Color.FromArgb(250, 250, 250);
            
            // 
            // btnClearLogs
            // 
            this.btnClearLogs.Location = new System.Drawing.Point(0, 510);
            this.btnClearLogs.Name = "btnClearLogs";
            this.btnClearLogs.Size = new System.Drawing.Size(100, 35);
            this.btnClearLogs.TabIndex = 1;
            this.btnClearLogs.Text = "清空日志";
            this.btnClearLogs.Type = AntdUI.TTypeMini.Default;
            this.btnClearLogs.Click += new System.EventHandler(this.btnClearLogs_Click);
            
            // 
            // Form1
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 17F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1000, 700);
            this.Controls.Add(this.mainPanel);
            this.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F);
            this.Name = "Form1";
            this.Text = "企业微信 Dify 集成";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.MinimumSize = new System.Drawing.Size(1000, 700);
            this.ResumeLayout(false);
        }

        #endregion

        // 主容器
        private AntdUI.Panel mainPanel;
        private AntdUI.Panel headerPanel;
        private AntdUI.Panel contentPanel;
        
        // 标题栏控件
        private AntdUI.Label lblTitle;
        private AntdUI.Label lblServiceStatus;
        private AntdUI.Button btnStartService;
        private AntdUI.Button btnStopService;
        private AntdUI.Button btnRestartService;
        
        // 选项卡控件
        private AntdUI.Tabs tabControl;
        private AntdUI.TabPage tabPageConfig;
        private AntdUI.TabPage tabPageMonitor;
        private AntdUI.TabPage tabPageLogs;
        private AntdUI.TabPage tabPageMessages;
        
        // 配置页面控件
        private AntdUI.Panel configPanel;
        private AntdUI.Panel difyConfigGroup;
        private AntdUI.Panel wechatConfigGroup;
        private AntdUI.Panel appConfigGroup;
        
        // Dify配置控件
        private AntdUI.Label lblDifyTitle;
        private AntdUI.Label lblDifyApiUrl;
        private AntdUI.Input txtDifyApiUrl;
        private AntdUI.Label lblDifyApiKey;
        private AntdUI.Input txtDifyApiKey;
        private AntdUI.Label lblDifyTimeout;
        private AntdUI.InputNumber numDifyTimeout;
        private AntdUI.Button btnTestDify;
        private AntdUI.Label lblDifyStatus;
        
        // 企业微信配置控件
        private AntdUI.Label lblWeChatTitle;
        private AntdUI.Label lblWeChatCorpId;
        private AntdUI.Input txtWeChatCorpId;
        private AntdUI.Label lblWeChatCorpSecret;
        private AntdUI.Input txtWeChatCorpSecret;
        private AntdUI.Label lblWeChatAgentId;
        private AntdUI.Input txtWeChatAgentId;
        private AntdUI.Button btnTestWeChat;
        private AntdUI.Label lblWeChatStatus;
        
        // 应用配置控件
        private AntdUI.Label lblAppTitle;
        private AntdUI.Checkbox chkAutoStart;
        private AntdUI.Checkbox chkMinimizeToTray;
        private AntdUI.Label lblLogRetentionDays;
        private AntdUI.InputNumber numLogRetentionDays;
        private AntdUI.Button btnSaveConfig;
        
        // 监控页面控件
        private AntdUI.Panel monitorPanel;
        private AntdUI.Panel statsPanel;
        private AntdUI.Label lblStatsTitle;
        private AntdUI.Label lblTotalMessagesLabel;
        private AntdUI.Label lblTotalMessages;
        private AntdUI.Label lblSuccessMessagesLabel;
        private AntdUI.Label lblSuccessMessages;
        private AntdUI.Label lblFailedMessagesLabel;
        private AntdUI.Label lblFailedMessages;
        private AntdUI.Label lblUptimeLabel;
        private AntdUI.Label lblUptime;
        
        // 日志页面控件
        private AntdUI.Panel logsPanel;
        private AntdUI.Input txtLogs;
        private AntdUI.Button btnClearLogs;
        
        // 消息页面控件
        private AntdUI.Panel messagesPanel;
        private System.Windows.Forms.ListView listMessages;
        private System.Windows.Forms.ColumnHeader colTime;
        private System.Windows.Forms.ColumnHeader colSender;
        private System.Windows.Forms.ColumnHeader colContent;
        private System.Windows.Forms.ColumnHeader colStatus;
    }
}

