using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace 企业微信.Models
{
    /// <summary>
    /// 应用配置模型
    /// </summary>
    public class AppConfig
    {
        /// <summary>
        /// Dify API配置
        /// </summary>
        public DifyConfig Dify { get; set; } = new DifyConfig();

        /// <summary>
        /// 企业微信配置
        /// </summary>
        public WeChatConfig WeChat { get; set; } = new WeChatConfig();

        /// <summary>
        /// 应用设置
        /// </summary>
        public AppSettings App { get; set; } = new AppSettings();

        /// <summary>
        /// 消息配置
        /// </summary>
        public MessageConfig Message { get; set; } = new MessageConfig();

        /// <summary>
        /// 自定义回复配置
        /// </summary>
        public List<CustomReplyConfig> CustomReplies { get; set; } = new List<CustomReplyConfig>();
    }

    /// <summary>
    /// Dify API配置
    /// </summary>
    public class DifyConfig
    {
        /// <summary>
        /// API地址
        /// </summary>
        public string ApiUrl { get; set; } = "https://api.dify.ai/v1";

        /// <summary>
        /// API密钥
        /// </summary>
        public string ApiKey { get; set; }

        /// <summary>
        /// 超时时间（秒）
        /// </summary>
        public int TimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; } = 3;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool Enabled { get; set; } = true;
    }

    /// <summary>
    /// 企业微信配置
    /// </summary>
    public class WeChatConfig
    {
        /// <summary>
        /// 企业ID
        /// </summary>
        public string CorpId { get; set; }

        /// <summary>
        /// 企业密钥
        /// </summary>
        public string CorpSecret { get; set; }

        /// <summary>
        /// 应用ID
        /// </summary>
        public string AgentId { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool Enabled { get; set; } = true;
    }

    /// <summary>
    /// 应用设置
    /// </summary>
    public class AppSettings
    {
        /// <summary>
        /// 应用名称
        /// </summary>
        public string Name { get; set; } = "Dify企业微信助手";

        /// <summary>
        /// 日志级别
        /// </summary>
        public string LogLevel { get; set; } = "INFO";

        /// <summary>
        /// 最大日志文件数
        /// </summary>
        public int MaxLogFiles { get; set; } = 10;

        /// <summary>
        /// 最大日志文件大小（MB）
        /// </summary>
        public int MaxLogSizeMB { get; set; } = 10;

        /// <summary>
        /// 自动启动
        /// </summary>
        public bool AutoStart { get; set; } = false;
    }

    /// <summary>
    /// 消息配置
    /// </summary>
    public class MessageConfig
    {
        /// <summary>
        /// 最大消息长度
        /// </summary>
        public int MaxLength { get; set; } = 2000;

        /// <summary>
        /// 消息超时时间（秒）
        /// </summary>
        public int TimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// 启用消息过滤
        /// </summary>
        public bool EnableFilter { get; set; } = true;

        /// <summary>
        /// 过滤关键词
        /// </summary>
        public List<string> FilterKeywords { get; set; } = new List<string>();
    }

    /// <summary>
    /// 自定义回复配置
    /// </summary>
    public class CustomReplyConfig
    {
        /// <summary>
        /// 规则ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 规则名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 触发关键词
        /// </summary>
        public List<string> Keywords { get; set; } = new List<string>();

        /// <summary>
        /// 回复内容
        /// </summary>
        public string ReplyContent { get; set; };

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// 匹配类型
        /// </summary>
        public MatchType MatchType { get; set; } = MatchType.Contains;

        /// <summary>
        /// 优先级
        /// </summary>
        public int Priority { get; set; } = 0;
    }

    /// <summary>
    /// 匹配类型枚举
    /// </summary>
    public enum MatchType
    {
        Exact = 1,      // 精确匹配
        Contains = 2,   // 包含匹配
        StartsWith = 3, // 开头匹配
        EndsWith = 4,   // 结尾匹配
        Regex = 5       // 正则匹配
    }
}