using System;
using System.IO;
using System.Threading.Tasks;
using Newtonsoft.Json;
using DotNetEnv;
using 企业微信.Models;

namespace 企业微信.Services
{
    /// <summary>
    /// 配置管理器
    /// </summary>
    public class ConfigManager : IConfigManager
    {
        private readonly string _configFilePath;
        private readonly string _envFilePath;
        private AppConfig _currentConfig;
        private readonly object _lockObject = new object();

        public event EventHandler<ConfigChangedEventArgs> ConfigChanged;

        public ConfigManager()
        {
            var appDir = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);
            _configFilePath = Path.Combine(appDir, "Config", "app.config.json");
            _envFilePath = Path.Combine(appDir, ".env");
            
            // 确保配置目录存在
            var configDir = Path.GetDirectoryName(_configFilePath);
            if (!Directory.Exists(configDir))
            {
                Directory.CreateDirectory(configDir);
            }
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        public async Task<AppConfig> LoadConfigAsync()
        {
            try
            {
                // 加载环境变量
                LoadEnvironmentVariables();

                AppConfig config;

                // 如果配置文件存在，从文件加载
                if (File.Exists(_configFilePath))
                {
                    var json = await File.ReadAllTextAsync(_configFilePath);
                    config = JsonConvert.DeserializeObject<AppConfig>(json) ?? new AppConfig();
                }
                else
                {
                    // 创建默认配置
                    config = CreateDefaultConfig();
                    await SaveConfigAsync(config);
                }

                // 从环境变量覆盖配置
                OverrideFromEnvironment(config);

                lock (_lockObject)
                {
                    _currentConfig = config;
                }

                return config;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"加载配置失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        public async Task SaveConfigAsync(AppConfig config)
        {
            try
            {
                var json = JsonConvert.SerializeObject(config, Formatting.Indented);
                await File.WriteAllTextAsync(_configFilePath, json);

                var oldConfig = _currentConfig;
                lock (_lockObject)
                {
                    _currentConfig = config;
                }

                // 触发配置变更事件
                ConfigChanged?.Invoke(this, new ConfigChangedEventArgs
                {
                    OldConfig = oldConfig,
                    NewConfig = config,
                    ChangedProperty = "All"
                });
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"保存配置失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取当前配置
        /// </summary>
        public AppConfig GetConfig()
        {
            lock (_lockObject)
            {
                return _currentConfig ?? new AppConfig();
            }
        }

        /// <summary>
        /// 更新配置
        /// </summary>
        public void UpdateConfig(AppConfig config)
        {
            var oldConfig = _currentConfig;
            lock (_lockObject)
            {
                _currentConfig = config;
            }

            // 触发配置变更事件
            ConfigChanged?.Invoke(this, new ConfigChangedEventArgs
            {
                OldConfig = oldConfig,
                NewConfig = config,
                ChangedProperty = "All"
            });
        }

        /// <summary>
        /// 加载环境变量
        /// </summary>
        private void LoadEnvironmentVariables()
        {
            try
            {
                if (File.Exists(_envFilePath))
                {
                    Env.Load(_envFilePath);
                }
            }
            catch (Exception ex)
            {
                // 环境变量加载失败不应该阻止应用启动
                System.Diagnostics.Debug.WriteLine($"加载环境变量失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从环境变量覆盖配置
        /// </summary>
        private void OverrideFromEnvironment(AppConfig config)
        {
            // Dify配置
            var difyApiUrl = Environment.GetEnvironmentVariable("DIFY_API_URL");
            if (!string.IsNullOrEmpty(difyApiUrl))
                config.Dify.ApiUrl = difyApiUrl;

            var difyApiKey = Environment.GetEnvironmentVariable("DIFY_API_KEY");
            if (!string.IsNullOrEmpty(difyApiKey))
                config.Dify.ApiKey = difyApiKey;

            // 企业微信配置
            var wechatCorpId = Environment.GetEnvironmentVariable("WECHAT_CORP_ID");
            if (!string.IsNullOrEmpty(wechatCorpId))
                config.WeChat.CorpId = wechatCorpId;

            var wechatCorpSecret = Environment.GetEnvironmentVariable("WECHAT_CORP_SECRET");
            if (!string.IsNullOrEmpty(wechatCorpSecret))
                config.WeChat.CorpSecret = wechatCorpSecret;

            var wechatAgentId = Environment.GetEnvironmentVariable("WECHAT_AGENT_ID");
            if (!string.IsNullOrEmpty(wechatAgentId))
                config.WeChat.AgentId = wechatAgentId;

            // 应用配置
            var appName = Environment.GetEnvironmentVariable("APP_NAME");
            if (!string.IsNullOrEmpty(appName))
                config.App.Name = appName;

            var logLevel = Environment.GetEnvironmentVariable("LOG_LEVEL");
            if (!string.IsNullOrEmpty(logLevel))
                config.App.LogLevel = logLevel;

            // 消息配置
            if (int.TryParse(Environment.GetEnvironmentVariable("MAX_MESSAGE_LENGTH"), out int maxLength))
                config.Message.MaxLength = maxLength;

            if (int.TryParse(Environment.GetEnvironmentVariable("MESSAGE_TIMEOUT_SECONDS"), out int timeout))
                config.Message.TimeoutSeconds = timeout;
        }

        /// <summary>
        /// 创建默认配置
        /// </summary>
        private AppConfig CreateDefaultConfig()
        {
            return new AppConfig
            {
                Dify = new DifyConfig
                {
                    ApiUrl = "https://api.dify.ai/v1",
                    ApiKey = "",
                    TimeoutSeconds = 30,
                    RetryCount = 3,
                    Enabled = true
                },
                WeChat = new WeChatConfig
                {
                    CorpId = "",
                    CorpSecret = "",
                    AgentId = "",
                    Enabled = true
                },
                App = new AppSettings
                {
                    Name = "Dify企业微信助手",
                    LogLevel = "INFO",
                    MaxLogFiles = 10,
                    MaxLogSizeMB = 10,
                    AutoStart = false
                },
                Message = new MessageConfig
                {
                    MaxLength = 2000,
                    TimeoutSeconds = 30,
                    EnableFilter = true,
                    FilterKeywords = new System.Collections.Generic.List<string>()
                },
                CustomReplies = new System.Collections.Generic.List<CustomReplyConfig>()
            };
        }

        /// <summary>
        /// 验证配置
        /// </summary>
        public bool ValidateConfig(AppConfig config, out string errorMessage)
        {
            errorMessage = null;

            // 验证Dify配置
            if (config.Dify.Enabled)
            {
                if (string.IsNullOrEmpty(config.Dify.ApiUrl))
                {
                    errorMessage = "Dify API地址不能为空";
                    return false;
                }

                if (string.IsNullOrEmpty(config.Dify.ApiKey))
                {
                    errorMessage = "Dify API密钥不能为空";
                    return false;
                }

                if (!Uri.TryCreate(config.Dify.ApiUrl, UriKind.Absolute, out _))
                {
                    errorMessage = "Dify API地址格式无效";
                    return false;
                }
            }

            // 验证企业微信配置
            if (config.WeChat.Enabled)
            {
                if (string.IsNullOrEmpty(config.WeChat.CorpId))
                {
                    errorMessage = "企业微信CorpId不能为空";
                    return false;
                }

                if (string.IsNullOrEmpty(config.WeChat.CorpSecret))
                {
                    errorMessage = "企业微信CorpSecret不能为空";
                    return false;
                }

                if (string.IsNullOrEmpty(config.WeChat.AgentId))
                {
                    errorMessage = "企业微信AgentId不能为空";
                    return false;
                }
            }

            // 验证消息配置
            if (config.Message.MaxLength <= 0)
            {
                errorMessage = "消息最大长度必须大于0";
                return false;
            }

            if (config.Message.TimeoutSeconds <= 0)
            {
                errorMessage = "消息超时时间必须大于0";
                return false;
            }

            return true;
        }
    }
}