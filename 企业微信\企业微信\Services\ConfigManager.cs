using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Newtonsoft.Json;
using 企业微信.Models;

namespace 企业微信.Services
{
    /// <summary>
    /// 配置管理器
    /// </summary>
    public class ConfigManager : IConfigManager
    {
        private readonly string _configFilePath;
        private readonly string _envFilePath;
        private AppConfig _currentConfig;
        private readonly object _lockObject = new object();

        public event EventHandler<ConfigChangedEventArgs> ConfigChanged;

        public ConfigManager()
        {
            var appDir = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);
            _configFilePath = Path.Combine(appDir, "Config", "app.config.json");
            _envFilePath = Path.Combine(appDir, ".env");
            
            // 确保配置目录存在
            var configDir = Path.GetDirectoryName(_configFilePath);
            if (!Directory.Exists(configDir))
            {
                Directory.CreateDirectory(configDir);
            }
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        public async Task<AppConfig> LoadConfigAsync()
        {
            try
            {
                // 加载环境变量
                LoadEnvironmentVariables();

                AppConfig config;

                // 如果配置文件存在，从文件加载
                if (File.Exists(_configFilePath))
                {
                    var json = await File.ReadAllTextAsync(_configFilePath);
                    config = JsonConvert.DeserializeObject<AppConfig>(json) ?? new AppConfig();
                }
                else
                {
                    // 创建默认配置
                    config = CreateDefaultConfig();
                    await SaveConfigAsync(config);
                }

                // 从环境变量覆盖配置
                OverrideFromEnvironment(config);

                lock (_lockObject)
                {
                    _currentConfig = config;
                }

                return config;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"加载配置失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        public async Task SaveConfigAsync(AppConfig config)
        {
            try
            {
                var json = JsonConvert.SerializeObject(config, Formatting.Indented);
                await File.WriteAllTextAsync(_configFilePath, json);

                var oldConfig = _currentConfig;
                lock (_lockObject)
                {
                    _currentConfig = config;
                }

                // 触发配置变更事件
                ConfigChanged?.Invoke(this, new ConfigChangedEventArgs
                {
                    OldConfig = oldConfig,
                    NewConfig = config,
                    ChangedProperty = "All"
                });
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"保存配置失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取当前配置
        /// </summary>
        public AppConfig GetConfig()
        {
            lock (_lockObject)
            {
                return _currentConfig ?? new AppConfig();
            }
        }

        /// <summary>
        /// 更新配置
        /// </summary>
        public void UpdateConfig(AppConfig config)
        {
            var oldConfig = _currentConfig;
            lock (_lockObject)
            {
                _currentConfig = config;
            }

            // 触发配置变更事件
            ConfigChanged?.Invoke(this, new ConfigChangedEventArgs
            {
                OldConfig = oldConfig,
                NewConfig = config,
                ChangedProperty = "All"
            });
        }

        /// <summary>
        /// 加载环境变量
        /// </summary>
        private void LoadEnvironmentVariables()
        {
            try
            {
                if (File.Exists(_envFilePath))
                {
                    // 简化的.env文件加载
                    var lines = File.ReadAllLines(_envFilePath);
                    foreach (var line in lines)
                    {
                        if (string.IsNullOrWhiteSpace(line) || line.StartsWith("#"))
                            continue;

                        var parts = line.Split('=', 2);
                        if (parts.Length == 2)
                        {
                            var key = parts[0].Trim();
                            var value = parts[1].Trim();
                            Environment.SetEnvironmentVariable(key, value);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // 环境变量加载失败不应该阻止应用启动
                System.Diagnostics.Debug.WriteLine($"加载环境变量失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从环境变量覆盖配置
        /// </summary>
        private void OverrideFromEnvironment(AppConfig config)
        {
            // Dify配置
            var difyApiUrl = Environment.GetEnvironmentVariable("DIFY_API_URL");
            if (!string.IsNullOrEmpty(difyApiUrl))
                config.Dify.ApiUrl = difyApiUrl;

            var difyApiKey = Environment.GetEnvironmentVariable("DIFY_API_KEY");
            if (!string.IsNullOrEmpty(difyApiKey))
                config.Dify.ApiKey = difyApiKey;

            // 企业微信配置
            var wechatCorpId = Environment.GetEnvironmentVariable("WECHAT_CORP_ID");
            if (!string.IsNullOrEmpty(wechatCorpId))
                config.WeChat.CorpId = wechatCorpId;

            var wechatCorpSecret = Environment.GetEnvironmentVariable("WECHAT_CORP_SECRET");
            if (!string.IsNullOrEmpty(wechatCorpSecret))
                config.WeChat.CorpSecret = wechatCorpSecret;

            var wechatAgentId = Environment.GetEnvironmentVariable("WECHAT_AGENT_ID");
            if (!string.IsNullOrEmpty(wechatAgentId))
                config.WeChat.AgentId = wechatAgentId;

            // 应用配置
            var appName = Environment.GetEnvironmentVariable("APP_NAME");
            if (!string.IsNullOrEmpty(appName))
                config.App.Name = appName;

            var logLevel = Environment.GetEnvironmentVariable("LOG_LEVEL");
            if (!string.IsNullOrEmpty(logLevel))
                config.App.LogLevel = logLevel;

            // 消息配置
            if (int.TryParse(Environment.GetEnvironmentVariable("MAX_MESSAGE_LENGTH"), out int maxLength))
                config.Message.MaxLength = maxLength;

            if (int.TryParse(Environment.GetEnvironmentVariable("MESSAGE_TIMEOUT_SECONDS"), out int timeout))
                config.Message.TimeoutSeconds = timeout;
        }

        /// <summary>
        /// 创建默认配置
        /// </summary>
        private AppConfig CreateDefaultConfig()
        {
            return new AppConfig
            {
                Dify = new DifyConfig
                {
                    ApiUrl = "https://api.dify.ai/v1",
                    ApiKey = "",
                    TimeoutSeconds = 30,
                    RetryCount = 3,
                    Enabled = true
                },
                WeChat = new WeChatConfig
                {
                    CorpId = "",
                    CorpSecret = "",
                    AgentId = "",
                    Enabled = true
                },
                App = new AppSettings
                {
                    Name = "Dify企业微信助手",
                    LogLevel = "INFO",
                    MaxLogFiles = 10,
                    MaxLogSizeMB = 10,
                    AutoStart = false
                },
                Message = new MessageConfig
                {
                    MaxLength = 2000,
                    TimeoutSeconds = 30,
                    EnableFilter = true,
                    FilterKeywords = new System.Collections.Generic.List<string>()
                },
                CustomReplies = new System.Collections.Generic.List<CustomReplyConfig>()
            };
        }

        /// <summary>
        /// 验证配置（接口实现）
        /// </summary>
        public ValidationResult ValidateConfig(AppConfig config = null)
        {
            config = config ?? GetConfig();
            var result = new ValidationResult();
            var errors = new List<string>();

            // 验证Dify配置
            if (config.Dify.Enabled)
            {
                if (string.IsNullOrEmpty(config.Dify.ApiUrl))
                    errors.Add("Dify API地址不能为空");

                if (string.IsNullOrEmpty(config.Dify.ApiKey))
                    errors.Add("Dify API密钥不能为空");

                if (!string.IsNullOrEmpty(config.Dify.ApiUrl) && !Uri.TryCreate(config.Dify.ApiUrl, UriKind.Absolute, out _))
                    errors.Add("Dify API地址格式无效");
            }

            // 验证企业微信配置
            if (config.WeChat.Enabled)
            {
                if (string.IsNullOrEmpty(config.WeChat.CorpId))
                    errors.Add("企业微信CorpId不能为空");

                if (string.IsNullOrEmpty(config.WeChat.CorpSecret))
                    errors.Add("企业微信CorpSecret不能为空");

                if (string.IsNullOrEmpty(config.WeChat.AgentId))
                    errors.Add("企业微信AgentId不能为空");
            }

            // 验证消息配置
            if (config.Message.MaxLength <= 0)
                errors.Add("消息最大长度必须大于0");

            if (config.Message.TimeoutSeconds <= 0)
                errors.Add("消息超时时间必须大于0");

            result.IsValid = errors.Count == 0;
            result.Errors = errors.ToArray();

            return result;
        }

        /// <summary>
        /// 验证配置（兼容方法）
        /// </summary>
        public bool ValidateConfig(AppConfig config, out string errorMessage)
        {
            var result = ValidateConfig(config);
            errorMessage = result.IsValid ? null : string.Join("; ", result.Errors);
            return result.IsValid;
        }

        /// <summary>
        /// 保存配置（接口实现）
        /// </summary>
        public bool SaveConfig(AppConfig config)
        {
            try
            {
                SaveConfigAsync(config).Wait();
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存配置失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 重新加载配置
        /// </summary>
        public bool ReloadConfig()
        {
            try
            {
                LoadConfigAsync().Wait();
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"重新加载配置失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取配置文件路径
        /// </summary>
        public string GetConfigFilePath()
        {
            return _configFilePath;
        }

        /// <summary>
        /// 重置为默认配置
        /// </summary>
        public AppConfig ResetToDefault()
        {
            var defaultConfig = CreateDefaultConfig();
            UpdateConfig(defaultConfig);
            return defaultConfig;
        }
    }
}