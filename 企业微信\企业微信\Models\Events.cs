using System;

namespace 企业微信.Models
{
    /// <summary>
    /// 服务状态变更事件参数
    /// </summary>
    public class ServiceStatusChangedEventArgs : EventArgs
    {
        public ServiceStatus OldStatus { get; set; }
        public ServiceStatus NewStatus { get; set; }
        public string Reason { get; set; }
        public DateTime Timestamp { get; set; }

        // 兼容性属性
        public ServiceStatus Status => NewStatus;
        public string Message => Reason;

        public ServiceStatusChangedEventArgs()
        {
            Timestamp = DateTime.Now;
        }

        public ServiceStatusChangedEventArgs(ServiceStatus oldStatus, ServiceStatus newStatus, string reason = "")
        {
            OldStatus = oldStatus;
            NewStatus = newStatus;
            Reason = reason ?? string.Empty;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 错误发生事件参数
    /// </summary>
    public class ErrorOccurredEventArgs : EventArgs
    {
        public string ErrorMessage { get; set; }
        public Exception Exception { get; set; }
        public string Source { get; set; }
        public DateTime Timestamp { get; set; }

        public ErrorOccurredEventArgs(string errorMessage, Exception exception = null, string source = "")
        {
            ErrorMessage = errorMessage ?? string.Empty;
            Exception = exception;
            Source = source ?? string.Empty;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 日志添加事件参数
    /// </summary>
    public class LogAddedEventArgs : EventArgs
    {
        public LogEntry LogEntry { get; set; }
        public DateTime Timestamp { get; set; }

        public LogAddedEventArgs()
        {
            Timestamp = DateTime.Now;
        }

        public LogAddedEventArgs(LogEntry logEntry)
        {
            LogEntry = logEntry;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 消息接收事件参数
    /// </summary>
    public class MessageReceivedEventArgs : EventArgs
    {
        public MessageModel Message { get; set; }
        public DateTime Timestamp { get; set; }

        public MessageReceivedEventArgs()
        {
            Timestamp = DateTime.Now;
        }

        public MessageReceivedEventArgs(MessageModel message)
        {
            Message = message;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 消息处理事件参数
    /// </summary>
    public class MessageProcessedEventArgs : EventArgs
    {
        public MessageModel OriginalMessage { get; set; }
        public MessageModel ProcessedMessage { get; set; }
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
        public DateTime Timestamp { get; set; }

        public MessageProcessedEventArgs()
        {
            Timestamp = DateTime.Now;
        }

        public MessageProcessedEventArgs(MessageModel originalMessage, MessageModel processedMessage, bool success, string errorMessage = "")
        {
            OriginalMessage = originalMessage;
            ProcessedMessage = processedMessage;
            Success = success;
            ErrorMessage = errorMessage ?? string.Empty;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 配置变更事件参数
    /// </summary>
    public class ConfigChangedEventArgs : EventArgs
    {
        public AppConfig OldConfig { get; set; }
        public AppConfig NewConfig { get; set; }
        public string ChangedProperty { get; set; }
        public DateTime Timestamp { get; set; }

        public ConfigChangedEventArgs()
        {
            Timestamp = DateTime.Now;
        }

        public ConfigChangedEventArgs(AppConfig oldConfig, AppConfig newConfig, string changedProperty = "")
        {
            OldConfig = oldConfig;
            NewConfig = newConfig;
            ChangedProperty = changedProperty ?? string.Empty;
            Timestamp = DateTime.Now;
        }
    }
}