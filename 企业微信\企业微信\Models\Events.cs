using System;

namespace 企业微信.Models
{
    /// <summary>
    /// 服务状态变更事件参数
    /// </summary>
    public class ServiceStatusChangedEventArgs : EventArgs
    {
        public ServiceStatus Status { get; set; }
        public string Message { get; set; }
        public DateTime Timestamp { get; set; }

        public ServiceStatusChangedEventArgs(ServiceStatus status, string message = "")
        {
            Status = status;
            Message = message ?? string.Empty;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 错误发生事件参数
    /// </summary>
    public class ErrorOccurredEventArgs : EventArgs
    {
        public string ErrorMessage { get; set; }
        public Exception Exception { get; set; }
        public string Source { get; set; }
        public DateTime Timestamp { get; set; }

        public ErrorOccurredEventArgs(string errorMessage, Exception exception = null, string source = "")
        {
            ErrorMessage = errorMessage ?? string.Empty;
            Exception = exception;
            Source = source ?? string.Empty;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 日志添加事件参数
    /// </summary>
    public class LogAddedEventArgs : EventArgs
    {
        public LogLevel Level { get; set; }
        public string Message { get; set; }
        public string Source { get; set; }
        public DateTime Timestamp { get; set; }

        public LogAddedEventArgs(LogLevel level, string message, string source = "")
        {
            Level = level;
            Message = message ?? string.Empty;
            Source = source ?? string.Empty;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 消息接收事件参数
    /// </summary>
    public class MessageReceivedEventArgs : EventArgs
    {
        public ChatMessage Message { get; set; }
        public DateTime Timestamp { get; set; }

        public MessageReceivedEventArgs(ChatMessage message)
        {
        Message = message;
            Timestamp = DateTime.Now;
        }
    }
}