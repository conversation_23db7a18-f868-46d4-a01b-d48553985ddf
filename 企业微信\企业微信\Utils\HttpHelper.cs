using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace 企业微信.Utils
{
    /// <summary>
    /// HTTP请求帮助类
    /// </summary>
    public static class HttpHelper
    {
        private static readonly HttpClient _httpClient = new HttpClient();

        static HttpHelper()
        {
            _httpClient.Timeout = TimeSpan.FromSeconds(30);
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "DifyWeChatBridge/1.0");
        }

        /// <summary>
        /// 发送GET请求
        /// </summary>
        public static async Task<string> GetAsync(string url, int timeoutSeconds = 30)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    client.Timeout = TimeSpan.FromSeconds(timeoutSeconds);
                    var response = await client.GetAsync(url);
                    response.EnsureSuccessStatusCode();
                    return await response.Content.ReadAsStringAsync();
                }
            }
            catch (Exception ex)
            {
                throw new HttpRequestException($"GET请求失败: {url}", ex);
            }
        }

        /// <summary>
        /// 发送POST请求
        /// </summary>
        public static async Task<string> PostAsync(string url, string jsonContent, int timeoutSeconds = 30)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    client.Timeout = TimeSpan.FromSeconds(timeoutSeconds);
                    var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
                    var response = await client.PostAsync(url, content);
                    response.EnsureSuccessStatusCode();
                    return await response.Content.ReadAsStringAsync();
                }
            }
            catch (Exception ex)
            {
                throw new HttpRequestException($"POST请求失败: {url}", ex);
            }
        }

        /// <summary>
        /// 发送POST请求（对象序列化）
        /// </summary>
        public static async Task<string> PostAsync<T>(string url, T data, int timeoutSeconds = 30)
        {
            var jsonContent = JsonConvert.SerializeObject(data);
            return await PostAsync(url, jsonContent, timeoutSeconds);
        }

        /// <summary>
        /// 发送GET请求并反序列化响应
        /// </summary>
        public static async Task<T> GetAsync<T>(string url, int timeoutSeconds = 30)
        {
            var response = await GetAsync(url, timeoutSeconds);
            return JsonConvert.DeserializeObject<T>(response);
        }

        /// <summary>
        /// 发送POST请求并反序列化响应
        /// </summary>
        public static async Task<TResponse> PostAsync<TRequest, TResponse>(string url, TRequest data, int timeoutSeconds = 30)
        {
            var response = await PostAsync(url, data, timeoutSeconds);
            return JsonConvert.DeserializeObject<TResponse>(response);
        }

        /// <summary>
        /// 检查URL是否可访问
        /// </summary>
        public static async Task<bool> IsUrlAccessibleAsync(string url, int timeoutSeconds = 10)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    client.Timeout = TimeSpan.FromSeconds(timeoutSeconds);
                    var response = await client.GetAsync(url);
                    return response.IsSuccessStatusCode;
                }
            }
            catch
            {
                return false;
            }
        }
    }
}