using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace 企业微信.Models
{


    /// <summary>
    /// 消息处理接口
    /// </summary>
    public interface IMessageProcessor
    {
        /// <summary>
        /// 处理消息
        /// </summary>
        Task<MessageModel> ProcessMessageAsync(MessageModel message);

        /// <summary>
        /// 检查消息是否需要处理
        /// </summary>
        bool ShouldProcessMessage(MessageModel message);

        /// <summary>
        /// 消息处理事件
        /// </summary>
        event EventHandler<MessageProcessedEventArgs> MessageProcessed;
    }



    /// <summary>
    /// Dify客户端接口
    /// </summary>
    public interface IDifyClient
    {
        /// <summary>
        /// 发送消息到Dify
        /// </summary>
        Task<string> SendMessageAsync(string message, string userId = null);

        /// <summary>
        /// 检查连接状态
        /// </summary>
        Task<bool> CheckConnectionAsync();

        /// <summary>
        /// 获取API状态
        /// </summary>
        Task<ApiStatus> GetApiStatusAsync();
    }

    /// <summary>
    /// 企业微信桥接接口
    /// </summary>
    public interface IWeChatBridge
    {
        /// <summary>
        /// 发送消息到企业微信
        /// </summary>
        Task<bool> SendMessageAsync(string toUser, string content);

        /// <summary>
        /// 获取用户信息
        /// </summary>
        Task<WeChatUser> GetUserInfoAsync(string userId);

        /// <summary>
        /// 检查连接状态
        /// </summary>
        Task<bool> CheckConnectionAsync();

        /// <summary>
        /// 消息接收事件
        /// </summary>
        event EventHandler<MessageReceivedEventArgs> MessageReceived;
    }





    #region 辅助模型

    /// <summary>
    /// 日志条目
    /// </summary>
    public class LogEntry
    {
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public LogLevel Level { get; set; }
        public string Message { get; set; }
        public string Exception { get; set; }
        public string Source { get; set; }
        public string Data { get; set; }
    }

    /// <summary>
    /// 日志级别
    /// </summary>
    public enum LogLevel
    {
        Debug = 1,
        Info = 2,
        Warning = 3,
        Error = 4
    }

    /// <summary>
    /// API状态
    /// </summary>
    public class ApiStatus
    {
        public bool IsConnected { get; set; }
        public string Version { get; set; }
        public long ResponseTimeMs { get; set; }
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// 企业微信用户
    /// </summary>
    public class WeChatUser
    {
        public string UserId { get; set; }
        public string Name { get; set; }
        public string Department { get; set; }
        public string Position { get; set; }
        public string Avatar { get; set; }
    }

    /// <summary>
    /// 服务状态
    /// </summary>
    public enum ServiceStatus
    {
        Stopped = 1,
        Starting = 2,
        Running = 3,
        Stopping = 4,
        Error = 5
    }

    #endregion
}