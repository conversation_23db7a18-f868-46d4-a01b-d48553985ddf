using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace 企业微信.Models
{
    /// <summary>
    /// 配置管理接口
    /// </summary>
    public interface IConfigManager
    {
        /// <summary>
        /// 加载配置
        /// </summary>
        Task<AppConfig> LoadConfigAsync();

        /// <summary>
        /// 保存配置
        /// </summary>
        Task SaveConfigAsync(AppConfig config);

        /// <summary>
        /// 获取配置
        /// </summary>
        AppConfig GetConfig();

        /// <summary>
        /// 更新配置
        /// </summary>
        void UpdateConfig(AppConfig config);

        /// <summary>
        /// 配置变更事件
        /// </summary>
        event EventHandler<ConfigChangedEventArgs> ConfigChanged;
    }

    /// <summary>
    /// 消息处理接口
    /// </summary>
    public interface IMessageProcessor
    {
        /// <summary>
        /// 处理消息
        /// </summary>
        Task<MessageModel> ProcessMessageAsync(MessageModel message);

        /// <summary>
        /// 检查消息是否需要处理
        /// </summary>
        bool ShouldProcessMessage(MessageModel message);

        /// <summary>
        /// 消息处理事件
        /// </summary>
        event EventHandler<MessageProcessedEventArgs> MessageProcessed;
    }

    /// <summary>
    /// 日志管理接口
    /// </summary>
    public interface ILogManager
    {
        /// <summary>
        /// 记录信息日志
        /// </summary>
        void LogInfo(string message, params object[] args);

        /// <summary>
        /// 记录警告日志
        /// </summary>
        void LogWarning(string message, params object[] args);

        /// <summary>
        /// 记录错误日志
        /// </summary>
        void LogError(string message, Exception exception = null, params object[] args);

        /// <summary>
        /// 记录调试日志
        /// </summary>
        void LogDebug(string message, params object[] args);

        /// <summary>
        /// 获取最近的日志
        /// </summary>
        List<LogEntry> GetRecentLogs(int count = 100);

        /// <summary>
        /// 清理旧日志
        /// </summary>
        void CleanupOldLogs();

        /// <summary>
        /// 日志记录事件
        /// </summary>
        event EventHandler<LogEntryEventArgs> LogEntryAdded;
    }

    /// <summary>
    /// Dify客户端接口
    /// </summary>
    public interface IDifyClient
    {
        /// <summary>
        /// 发送消息到Dify
        /// </summary>
        Task<string> SendMessageAsync(string message, string userId = null);

        /// <summary>
        /// 检查连接状态
        /// </summary>
        Task<bool> CheckConnectionAsync();

        /// <summary>
        /// 获取API状态
        /// </summary>
        Task<ApiStatus> GetApiStatusAsync();
    }

    /// <summary>
    /// 企业微信桥接接口
    /// </summary>
    public interface IWeChatBridge
    {
        /// <summary>
        /// 发送消息到企业微信
        /// </summary>
        Task<bool> SendMessageAsync(string toUser, string content);

        /// <summary>
        /// 获取用户信息
        /// </summary>
        Task<WeChatUser> GetUserInfoAsync(string userId);

        /// <summary>
        /// 检查连接状态
        /// </summary>
        Task<bool> CheckConnectionAsync();

        /// <summary>
        /// 消息接收事件
        /// </summary>
        event EventHandler<MessageReceivedEventArgs> MessageReceived;
    }

    /// <summary>
    /// 服务控制器接口
    /// </summary>
    public interface IServiceController
    {
        /// <summary>
        /// 启动服务
        /// </summary>
        Task<bool> StartAsync();

        /// <summary>
        /// 停止服务
        /// </summary>
        Task<bool> StopAsync();

        /// <summary>
        /// 重启服务
        /// </summary>
        Task<bool> RestartAsync();

        /// <summary>
        /// 获取服务状态
        /// </summary>
        ServiceStatus GetStatus();

        /// <summary>
        /// 服务状态变更事件
        /// </summary>
        event EventHandler<ServiceStatusChangedEventArgs> StatusChanged;
    }

    #region 事件参数类

    /// <summary>
    /// 配置变更事件参数
    /// </summary>
    public class ConfigChangedEventArgs : EventArgs
    {
        public AppConfig OldConfig { get; set; }
        public AppConfig NewConfig { get; set; }
        public string ChangedProperty { get; set; }
    }

    /// <summary>
    /// 消息处理事件参数
    /// </summary>
    public class MessageProcessedEventArgs : EventArgs
    {
        public MessageModel OriginalMessage { get; set; }
        public MessageModel ProcessedMessage { get; set; }
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// 日志条目事件参数
    /// </summary>
    public class LogEntryEventArgs : EventArgs
    {
        public LogEntry LogEntry { get; set; }
    }

    /// <summary>
    /// 消息接收事件参数
    /// </summary>
    public class MessageReceivedEventArgs : EventArgs
    {
        public MessageModel Message { get; set; }
    }

    /// <summary>
    /// 服务状态变更事件参数
    /// </summary>
    public class ServiceStatusChangedEventArgs : EventArgs
    {
        public ServiceStatus OldStatus { get; set; }
        public ServiceStatus NewStatus { get; set; }
        public string Reason { get; set; }
    }

    #endregion

    #region 辅助模型

    /// <summary>
    /// 日志条目
    /// </summary>
    public class LogEntry
    {
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public LogLevel Level { get; set; }
        public string Message { get; set; }
        public string Exception { get; set; }
        public string Source { get; set; }
    }

    /// <summary>
    /// 日志级别
    /// </summary>
    public enum LogLevel
    {
        Debug = 1,
        Info = 2,
        Warning = 3,
        Error = 4
    }

    /// <summary>
    /// API状态
    /// </summary>
    public class ApiStatus
    {
        public bool IsConnected { get; set; }
        public string Version { get; set; }
        public long ResponseTimeMs { get; set; }
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// 企业微信用户
    /// </summary>
    public class WeChatUser
    {
        public string UserId { get; set; }
        public string Name { get; set; }
        public string Department { get; set; }
        public string Position { get; set; }
        public string Avatar { get; set; }
    }

    /// <summary>
    /// 服务状态
    /// </summary>
    public enum ServiceStatus
    {
        Stopped = 1,
        Starting = 2,
        Running = 3,
        Stopping = 4,
        Error = 5
    }

    #endregion
}