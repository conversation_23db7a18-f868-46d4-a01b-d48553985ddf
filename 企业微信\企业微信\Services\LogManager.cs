using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using 企业微信.Models;

namespace 企业微信.Services
{
    /// <summary>
    /// 日志管理器
    /// </summary>
    public class LogManager : ILogManager, IDisposable
    {
        private readonly IConfigManager _configManager;
        private readonly Queue<LogEntry> _logQueue = new Queue<LogEntry>();
        private readonly object _lockObject = new object();
        private readonly Timer _flushTimer;
        private readonly string _logDirectory;
        private bool _disposed = false;

        public event EventHandler<LogAddedEventArgs> LogAdded;

        public LogManager(IConfigManager configManager)
        {
            _configManager = configManager ?? throw new ArgumentNullException(nameof(configManager));
            
            // 获取日志目录
            var config = _configManager.GetConfig();
            _logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
            
            // 确保日志目录存在
            if (!Directory.Exists(_logDirectory))
            {
                Directory.CreateDirectory(_logDirectory);
            }

            // 创建定时刷新器，每5秒刷新一次日志
            _flushTimer = new Timer(FlushLogs, null, TimeSpan.FromSeconds(5), TimeSpan.FromSeconds(5));
        }

        /// <summary>
        /// 记录信息日志
        /// </summary>
        public void LogInfo(string message, object data = null)
        {
            AddLogEntry(LogLevel.Info, message, null, data);
        }

        /// <summary>
        /// 记录警告日志
        /// </summary>
        public void LogWarning(string message, object data = null)
        {
            AddLogEntry(LogLevel.Warning, message, null, data);
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        public void LogError(string message, Exception exception = null, object data = null)
        {
            AddLogEntry(LogLevel.Error, message, exception, data);
        }

        /// <summary>
        /// 记录调试日志
        /// </summary>
        public void LogDebug(string message, object data = null)
        {
            AddLogEntry(LogLevel.Debug, message, null, data);
        }

        /// <summary>
        /// 获取日志条目
        /// </summary>
        public List<LogEntry> GetLogEntries(LogLevel? level = null, DateTime? startTime = null, DateTime? endTime = null, int maxCount = 1000)
        {
            var result = new List<LogEntry>();

            try
            {
                // 从内存中获取最新的日志
                lock (_lockObject)
                {
                    var memoryLogs = _logQueue.ToList();
                    result.AddRange(memoryLogs);
                }

                // 从文件中读取历史日志
                var todayLogFile = GetLogFilePath(DateTime.Today);
                if (File.Exists(todayLogFile))
                {
                    var fileLogs = ReadLogEntriesFromFile(todayLogFile);
                    result.AddRange(fileLogs);
                }

                // 如果需要读取更多天的日志
                if (startTime.HasValue && startTime.Value.Date < DateTime.Today)
                {
                    var currentDate = startTime.Value.Date;
                    while (currentDate < DateTime.Today && result.Count < maxCount)
                    {
                        var logFile = GetLogFilePath(currentDate);
                        if (File.Exists(logFile))
                        {
                            var fileLogs = ReadLogEntriesFromFile(logFile);
                            result.AddRange(fileLogs);
                        }
                        currentDate = currentDate.AddDays(1);
                    }
                }

                // 应用过滤条件
                var filteredLogs = result.AsEnumerable();

                if (level.HasValue)
                {
                    filteredLogs = filteredLogs.Where(log => log.Level == level.Value);
                }

                if (startTime.HasValue)
                {
                    filteredLogs = filteredLogs.Where(log => log.Timestamp >= startTime.Value);
                }

                if (endTime.HasValue)
                {
                    filteredLogs = filteredLogs.Where(log => log.Timestamp <= endTime.Value);
                }

                // 按时间倒序排列，取最新的记录
                return filteredLogs
                    .OrderByDescending(log => log.Timestamp)
                    .Take(maxCount)
                    .ToList();
            }
            catch (Exception ex)
            {
                // 日志系统本身出错时，至少返回错误信息
                return new List<LogEntry>
                {
                    new LogEntry
                    {
                        Level = LogLevel.Error,
                        Message = $"获取日志条目失败: {ex.Message}",
                        Timestamp = DateTime.Now,
                        Exception = ex.ToString()
                    }
                };
            }
        }

        /// <summary>
        /// 清理旧日志
        /// </summary>
        public async Task CleanupOldLogsAsync()
        {
            try
            {
                var config = _configManager.GetConfig();
                var retentionDays = config.App.LogRetentionDays;
                var cutoffDate = DateTime.Today.AddDays(-retentionDays);

                await Task.Run(() =>
                {
                    var logFiles = Directory.GetFiles(_logDirectory, "*.log");
                    
                    foreach (var logFile in logFiles)
                    {
                        var fileInfo = new FileInfo(logFile);
                        if (fileInfo.CreationTime.Date < cutoffDate)
                        {
                            try
                            {
                                File.Delete(logFile);
                                LogInfo($"删除过期日志文件: {Path.GetFileName(logFile)}");
                            }
                            catch (Exception ex)
                            {
                                LogError($"删除日志文件失败: {Path.GetFileName(logFile)}", ex);
                            }
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                LogError("清理旧日志失败", ex);
            }
        }

        /// <summary>
        /// 添加日志条目
        /// </summary>
        private void AddLogEntry(LogLevel level, string message, Exception exception = null, object data = null)
        {
            if (_disposed) return;

            try
            {
                var config = _configManager.GetConfig();
                
                // 检查日志级别是否需要记录
                if (!ShouldLog(level, config.App.LogLevel))
                {
                    return;
                }

                var logEntry = new LogEntry
                {
                    Level = level,
                    Message = message,
                    Timestamp = DateTime.Now,
                    Exception = exception?.ToString(),
                    Data = data?.ToString()
                };

                lock (_lockObject)
                {
                    _logQueue.Enqueue(logEntry);
                    
                    // 限制内存中的日志数量
                    while (_logQueue.Count > 1000)
                    {
                        _logQueue.Dequeue();
                    }
                }

                // 触发事件
                LogAdded?.Invoke(this, new LogAddedEventArgs { LogEntry = logEntry });

                // 如果是错误级别，立即刷新到文件
                if (level == LogLevel.Error)
                {
                    Task.Run(() => FlushLogs(null));
                }
            }
            catch (Exception ex)
            {
                // 日志系统本身出错时，写入系统事件日志或控制台
                Console.WriteLine($"日志记录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 判断是否应该记录日志
        /// </summary>
        private bool ShouldLog(LogLevel level, LogLevel configLevel)
        {
            return (int)level >= (int)configLevel;
        }

        /// <summary>
        /// 刷新日志到文件
        /// </summary>
        private void FlushLogs(object state)
        {
            if (_disposed) return;

            try
            {
                List<LogEntry> logsToFlush;
                
                lock (_lockObject)
                {
                    if (_logQueue.Count == 0) return;
                    
                    logsToFlush = new List<LogEntry>(_logQueue);
                    _logQueue.Clear();
                }

                var logFile = GetLogFilePath(DateTime.Today);
                var logLines = logsToFlush.Select(FormatLogEntry);

                File.AppendAllLines(logFile, logLines);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"刷新日志到文件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取日志文件路径
        /// </summary>
        private string GetLogFilePath(DateTime date)
        {
            var fileName = $"app_{date:yyyyMMdd}.log";
            return Path.Combine(_logDirectory, fileName);
        }

        /// <summary>
        /// 格式化日志条目
        /// </summary>
        private string FormatLogEntry(LogEntry entry)
        {
            var parts = new List<string>
            {
                entry.Timestamp.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                $"[{entry.Level}]",
                entry.Message
            };

            if (!string.IsNullOrEmpty(entry.Exception))
            {
                parts.Add($"Exception: {entry.Exception}");
            }

            if (!string.IsNullOrEmpty(entry.Data))
            {
                parts.Add($"Data: {entry.Data}");
            }

            return string.Join(" | ", parts);
        }

        /// <summary>
        /// 从文件读取日志条目
        /// </summary>
        private List<LogEntry> ReadLogEntriesFromFile(string filePath)
        {
            var entries = new List<LogEntry>();

            try
            {
                var lines = File.ReadAllLines(filePath);
                
                foreach (var line in lines)
                {
                    if (TryParseLogEntry(line, out LogEntry entry))
                    {
                        entries.Add(entry);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取日志文件失败: {ex.Message}");
            }

            return entries;
        }

        /// <summary>
        /// 解析日志条目
        /// </summary>
        private bool TryParseLogEntry(string line, out LogEntry entry)
        {
            entry = null;

            try
            {
                var parts = line.Split(new[] { " | " }, StringSplitOptions.None);
                if (parts.Length < 3) return false;

                var timestampStr = parts[0];
                var levelStr = parts[1].Trim('[', ']');
                var message = parts[2];

                if (!DateTime.TryParse(timestampStr, out DateTime timestamp)) return false;
                if (!Enum.TryParse<LogLevel>(levelStr, out LogLevel level)) return false;

                entry = new LogEntry
                {
                    Timestamp = timestamp,
                    Level = level,
                    Message = message
                };

                // 解析异常信息
                if (parts.Length > 3 && parts[3].StartsWith("Exception: "))
                {
                    entry.Exception = parts[3].Substring("Exception: ".Length);
                }

                // 解析数据信息
                if (parts.Length > 4 && parts[4].StartsWith("Data: "))
                {
                    entry.Data = parts[4].Substring("Data: ".Length);
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed) return;

            _disposed = true;
            
            _flushTimer?.Dispose();
            
            // 最后一次刷新日志
            FlushLogs(null);
        }
    }
}