using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using 企业微信.Models;

namespace 企业微信.Services
{
    /// <summary>
    /// 企业微信桥接器
    /// </summary>
    public class WeChatBridge : IWeChatBridge, IDisposable
    {
        private readonly IConfigManager _configManager;
        private readonly ILogManager _logManager;
        private readonly HttpClient _httpClient;
        private string _accessToken;
        private DateTime _tokenExpireTime;

        public event EventHandler<MessageReceivedEventArgs> MessageReceived;

        public WeChatBridge(IConfigManager configManager, ILogManager logManager)
        {
            _configManager = configManager ?? throw new ArgumentNullException(nameof(configManager));
            _logManager = logManager ?? throw new ArgumentNullException(nameof(logManager));
            
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromSeconds(30);
        }

        /// <summary>
        /// 检查连接状态
        /// </summary>
        public async Task<bool> CheckConnectionAsync()
        {
            try
            {
                var config = _configManager.GetConfig();
                if (!config.WeChat.Enabled)
                {
                    _logManager.LogInfo("企业微信未启用");
                    return false;
                }

                // 尝试获取访问令牌
                var token = await GetAccessTokenAsync();
                return !string.IsNullOrEmpty(token);
            }
            catch (Exception ex)
            {
                _logManager.LogError("检查企业微信连接失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 发送消息
        /// </summary>
        public async Task<bool> SendMessageAsync(MessageModel message)
        {
            if (message == null)
            {
                throw new ArgumentNullException(nameof(message));
            }

            try
            {
                var config = _configManager.GetConfig();
                if (!config.WeChat.Enabled)
                {
                    _logManager.LogWarning("企业微信未启用，无法发送消息");
                    return false;
                }

                var accessToken = await GetAccessTokenAsync();
                if (string.IsNullOrEmpty(accessToken))
                {
                    _logManager.LogError("获取访问令牌失败，无法发送消息");
                    return false;
                }

                var sendUrl = $"https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={accessToken}";

                var messageData = new
                {
                    touser = message.ReceiverId,
                    msgtype = "text",
                    agentid = config.WeChat.AgentId,
                    text = new
                    {
                        content = message.Content
                    },
                    safe = 0
                };

                var jsonContent = JsonConvert.SerializeObject(messageData);
                var httpContent = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                _logManager.LogDebug($"发送企业微信消息: {jsonContent}");

                var response = await _httpClient.PostAsync(sendUrl, httpContent);
                var responseContent = await response.Content.ReadAsStringAsync();

                _logManager.LogDebug($"企业微信响应: {responseContent}");

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<WeChatApiResponse>(responseContent);
                    if (result.errcode == 0)
                    {
                        _logManager.LogInfo($"企业微信消息发送成功: {message.Id}");
                        return true;
                    }
                    else
                    {
                        _logManager.LogError($"企业微信消息发送失败: {result.errcode} - {result.errmsg}");
                        return false;
                    }
                }
                else
                {
                    _logManager.LogError($"企业微信API调用失败: {response.StatusCode} - {responseContent}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logManager.LogError($"发送企业微信消息异常: {message.Id}", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取用户信息
        /// </summary>
        public async Task<WeChatUser> GetUserInfoAsync(string userId)
        {
            if (string.IsNullOrEmpty(userId))
            {
                throw new ArgumentNullException(nameof(userId));
            }

            try
            {
                var config = _configManager.GetConfig();
                if (!config.WeChat.Enabled)
                {
                    return null;
                }

                var accessToken = await GetAccessTokenAsync();
                if (string.IsNullOrEmpty(accessToken))
                {
                    return null;
                }

                var userUrl = $"https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token={accessToken}&userid={userId}";

                var response = await _httpClient.GetAsync(userUrl);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<WeChatUserResponse>(responseContent);
                    if (result.errcode == 0)
                    {
                        return new WeChatUser
                        {
                            UserId = result.userid,
                            Name = result.name,
                            Department = result.department,
                            Position = result.position,
                            Mobile = result.mobile,
                            Email = result.email,
                            Avatar = result.avatar
                        };
                    }
                    else
                    {
                        _logManager.LogError($"获取用户信息失败: {result.errcode} - {result.errmsg}");
                    }
                }
                else
                {
                    _logManager.LogError($"获取用户信息API调用失败: {response.StatusCode}");
                }

                return null;
            }
            catch (Exception ex)
            {
                _logManager.LogError($"获取用户信息异常: {userId}", ex);
                return null;
            }
        }

        /// <summary>
        /// 处理接收到的消息（用于Webhook回调）
        /// </summary>
        public void HandleIncomingMessage(string messageData)
        {
            try
            {
                if (string.IsNullOrEmpty(messageData))
                {
                    return;
                }

                _logManager.LogDebug($"收到企业微信消息: {messageData}");

                // 解析消息数据
                var wechatMessage = JsonConvert.DeserializeObject<WeChatIncomingMessage>(messageData);
                
                if (wechatMessage == null)
                {
                    _logManager.LogWarning("无法解析企业微信消息数据");
                    return;
                }

                // 转换为内部消息模型
                var message = new MessageModel
                {
                    Id = Guid.NewGuid().ToString(),
                    SenderId = wechatMessage.FromUserName,
                    ReceiverId = wechatMessage.ToUserName,
                    Content = wechatMessage.Content,
                    Type = GetMessageType(wechatMessage.MsgType),
                    Direction = MessageDirection.Incoming,
                    CreatedAt = DateTime.Now,
                    Status = MessageStatus.Pending
                };

                // 触发消息接收事件
                MessageReceived?.Invoke(this, new MessageReceivedEventArgs { Message = message });

                _logManager.LogInfo($"处理企业微信消息: {message.Id}");
            }
            catch (Exception ex)
            {
                _logManager.LogError("处理企业微信消息异常", ex);
            }
        }

        /// <summary>
        /// 获取访问令牌
        /// </summary>
        private async Task<string> GetAccessTokenAsync()
        {
            try
            {
                // 检查现有令牌是否有效
                if (!string.IsNullOrEmpty(_accessToken) && DateTime.Now < _tokenExpireTime)
                {
                    return _accessToken;
                }

                var config = _configManager.GetConfig();
                var tokenUrl = $"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={config.WeChat.CorpId}&corpsecret={config.WeChat.CorpSecret}";

                var response = await _httpClient.GetAsync(tokenUrl);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<WeChatTokenResponse>(responseContent);
                    if (result.errcode == 0)
                    {
                        _accessToken = result.access_token;
                        _tokenExpireTime = DateTime.Now.AddSeconds(result.expires_in - 300); // 提前5分钟过期
                        
                        _logManager.LogInfo("企业微信访问令牌获取成功");
                        return _accessToken;
                    }
                    else
                    {
                        _logManager.LogError($"获取企业微信访问令牌失败: {result.errcode} - {result.errmsg}");
                    }
                }
                else
                {
                    _logManager.LogError($"企业微信令牌API调用失败: {response.StatusCode}");
                }

                return null;
            }
            catch (Exception ex)
            {
                _logManager.LogError("获取企业微信访问令牌异常", ex);
                return null;
            }
        }

        /// <summary>
        /// 获取消息类型
        /// </summary>
        private MessageType GetMessageType(string msgType)
        {
            switch (msgType?.ToLower())
            {
                case "text":
                    return MessageType.Text;
                case "image":
                    return MessageType.Image;
                case "voice":
                    return MessageType.Voice;
                case "video":
                    return MessageType.Video;
                case "file":
                    return MessageType.File;
                default:
                    return MessageType.Text;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    #region 企业微信API响应模型

    /// <summary>
    /// 企业微信API响应基类
    /// </summary>
    public class WeChatApiResponse
    {
        public int errcode { get; set; }
        public string errmsg { get; set; }
    }

    /// <summary>
    /// 企业微信令牌响应
    /// </summary>
    public class WeChatTokenResponse : WeChatApiResponse
    {
        public string access_token { get; set; }
        public int expires_in { get; set; }
    }

    /// <summary>
    /// 企业微信用户响应
    /// </summary>
    public class WeChatUserResponse : WeChatApiResponse
    {
        public string userid { get; set; }
        public string name { get; set; }
        public int[] department { get; set; }
        public string position { get; set; }
        public string mobile { get; set; }
        public string email { get; set; }
        public string avatar { get; set; }
    }

    /// <summary>
    /// 企业微信接收消息模型
    /// </summary>
    public class WeChatIncomingMessage
    {
        public string ToUserName { get; set; }
        public string FromUserName { get; set; }
        public long CreateTime { get; set; }
        public string MsgType { get; set; }
        public string Content { get; set; }
        public long MsgId { get; set; }
        public int AgentID { get; set; }
    }

    #endregion
}