using System;
using System.Threading;
using System.Threading.Tasks;
using 企业微信.Models;

namespace 企业微信.Services
{
    /// <summary>
    /// 服务控制器
    /// </summary>
    public class ServiceController : IServiceController, IDisposable
    {
        private readonly IConfigManager _configManager;
        private readonly ILogManager _logManager;
        private readonly IDifyClient _difyClient;
        private readonly IWeChatBridge _wechatBridge;
        private readonly IMessageProcessor _messageProcessor;
        
        private ServiceStatus _status = ServiceStatus.Stopped;
        private CancellationTokenSource _cancellationTokenSource;
        private Task _serviceTask;
        private readonly object _lockObject = new object();

        public event EventHandler<ServiceStatusChangedEventArgs> ServiceStatusChanged;
        public event EventHandler<MessageReceivedEventArgs> MessageReceived;
        public event EventHandler<ErrorOccurredEventArgs> ErrorOccurred;

        // 为了兼容性保留原有事件
        public event EventHandler<ServiceStatusChangedEventArgs> StatusChanged;

        /// <summary>
        /// 当前服务状态
        /// </summary>
        public ServiceStatus Status => GetStatus();

        /// <summary>
        /// 是否正在运行
        /// </summary>
        public bool IsRunning => GetStatus() == ServiceStatus.Running;

        public ServiceController(
            IConfigManager configManager,
            ILogManager logManager,
            IDifyClient difyClient,
            IWeChatBridge wechatBridge,
            IMessageProcessor messageProcessor)
        {
            _configManager = configManager ?? throw new ArgumentNullException(nameof(configManager));
            _logManager = logManager ?? throw new ArgumentNullException(nameof(logManager));
            _difyClient = difyClient ?? throw new ArgumentNullException(nameof(difyClient));
            _wechatBridge = wechatBridge ?? throw new ArgumentNullException(nameof(wechatBridge));
            _messageProcessor = messageProcessor ?? throw new ArgumentNullException(nameof(messageProcessor));

            // 订阅消息接收事件
            _wechatBridge.MessageReceived += OnMessageReceived;
        }

        /// <summary>
        /// 启动服务
        /// </summary>
        public async Task<bool> StartAsync()
        {
            lock (_lockObject)
            {
                if (_status == ServiceStatus.Running || _status == ServiceStatus.Starting)
                {
                    _logManager.LogWarning("服务已在运行或正在启动中");
                    return false;
                }

                ChangeStatus(ServiceStatus.Starting, "正在启动服务");
            }

            try
            {
                _logManager.LogInfo("开始启动服务...");

                // 验证配置
                var config = _configManager.GetConfig();
                if (!_configManager.ValidateConfig(config, out string errorMessage))
                {
                    ChangeStatus(ServiceStatus.Error, $"配置验证失败: {errorMessage}");
                    return false;
                }

                // 检查Dify连接
                if (config.Dify.Enabled)
                {
                    _logManager.LogInfo("检查Dify连接...");
                    var difyConnected = await _difyClient.CheckConnectionAsync();
                    if (!difyConnected)
                    {
                        ChangeStatus(ServiceStatus.Error, "Dify连接失败");
                        return false;
                    }
                    _logManager.LogInfo("Dify连接正常");
                }

                // 检查企业微信连接
                if (config.WeChat.Enabled)
                {
                    _logManager.LogInfo("检查企业微信连接...");
                    var wechatConnected = await _wechatBridge.CheckConnectionAsync();
                    if (!wechatConnected)
                    {
                        ChangeStatus(ServiceStatus.Error, "企业微信连接失败");
                        return false;
                    }
                    _logManager.LogInfo("企业微信连接正常");
                }

                // 启动服务循环
                _cancellationTokenSource = new CancellationTokenSource();
                _serviceTask = RunServiceLoopAsync(_cancellationTokenSource.Token);

                ChangeStatus(ServiceStatus.Running, "服务启动成功");
                _logManager.LogInfo("服务启动成功");
                return true;
            }
            catch (Exception ex)
            {
                ChangeStatus(ServiceStatus.Error, $"服务启动失败: {ex.Message}");
                _logManager.LogError("服务启动失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 停止服务
        /// </summary>
        public async Task<bool> StopAsync()
        {
            lock (_lockObject)
            {
                if (_status == ServiceStatus.Stopped || _status == ServiceStatus.Stopping)
                {
                    _logManager.LogWarning("服务已停止或正在停止中");
                    return false;
                }

                ChangeStatus(ServiceStatus.Stopping, "正在停止服务");
            }

            try
            {
                _logManager.LogInfo("开始停止服务...");

                // 取消服务循环
                _cancellationTokenSource?.Cancel();

                // 等待服务任务完成
                if (_serviceTask != null)
                {
                    await _serviceTask;
                    _serviceTask = null;
                }

                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;

                ChangeStatus(ServiceStatus.Stopped, "服务停止成功");
                _logManager.LogInfo("服务停止成功");
                return true;
            }
            catch (Exception ex)
            {
                ChangeStatus(ServiceStatus.Error, $"服务停止失败: {ex.Message}");
                _logManager.LogError("服务停止失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 重启服务
        /// </summary>
        public async Task<bool> RestartAsync()
        {
            _logManager.LogInfo("开始重启服务...");

            var stopResult = await StopAsync();
            if (!stopResult)
            {
                _logManager.LogError("重启失败：停止服务失败");
                return false;
            }

            // 等待一小段时间确保资源完全释放
            await Task.Delay(1000);

            var startResult = await StartAsync();
            if (!startResult)
            {
                _logManager.LogError("重启失败：启动服务失败");
                return false;
            }

            _logManager.LogInfo("服务重启成功");
            return true;
        }

        /// <summary>
        /// 获取服务状态
        /// </summary>
        public ServiceStatus GetStatus()
        {
            lock (_lockObject)
            {
                return _status;
            }
        }

        /// <summary>
        /// 服务运行循环
        /// </summary>
        private async Task RunServiceLoopAsync(CancellationToken cancellationToken)
        {
            _logManager.LogInfo("服务循环开始运行");

            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    try
                    {
                        // 定期检查服务健康状态
                        await CheckServiceHealthAsync();

                        // 等待一段时间再进行下次检查
                        await Task.Delay(TimeSpan.FromMinutes(1), cancellationToken);
                    }
                    catch (OperationCanceledException)
                    {
                        // 正常取消，退出循环
                        break;
                    }
                    catch (Exception ex)
                    {
                        _logManager.LogError("服务循环异常", ex);
                        
                        // 如果是严重错误，改变服务状态
                        if (IsCriticalError(ex))
                        {
                            ChangeStatus(ServiceStatus.Error, $"服务运行异常: {ex.Message}");
                            break;
                        }

                        // 非严重错误，等待后继续
                        await Task.Delay(TimeSpan.FromSeconds(30), cancellationToken);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // 正常取消
            }
            finally
            {
                _logManager.LogInfo("服务循环结束");
            }
        }

        /// <summary>
        /// 检查服务健康状态
        /// </summary>
        private async Task CheckServiceHealthAsync()
        {
            var config = _configManager.GetConfig();

            // 检查Dify连接
            if (config.Dify.Enabled)
            {
                var difyStatus = await _difyClient.GetApiStatusAsync();
                if (!difyStatus.IsConnected)
                {
                    _logManager.LogWarning($"Dify连接异常: {difyStatus.ErrorMessage}");
                }
            }

            // 检查企业微信连接
            if (config.WeChat.Enabled)
            {
                var wechatConnected = await _wechatBridge.CheckConnectionAsync();
                if (!wechatConnected)
                {
                    _logManager.LogWarning("企业微信连接异常");
                }
            }
        }

        /// <summary>
        /// 消息接收处理
        /// </summary>
        private async void OnMessageReceived(object sender, MessageReceivedEventArgs e)
        {
            try
            {
                if (_status != ServiceStatus.Running)
                {
                    _logManager.LogWarning("服务未运行，忽略消息");
                    return;
                }

                _logManager.LogInfo($"收到消息: {e.Message.Content}");

                // 处理消息
                var processedMessage = await _messageProcessor.ProcessMessageAsync(e.Message);
                
                _logManager.LogInfo($"消息处理完成: {processedMessage.Status}");
            }
            catch (Exception ex)
            {
                _logManager.LogError("处理接收消息异常", ex);
            }
        }

        /// <summary>
        /// 改变服务状态
        /// </summary>
        private void ChangeStatus(ServiceStatus newStatus, string reason = null)
        {
            ServiceStatus oldStatus;
            
            lock (_lockObject)
            {
                oldStatus = _status;
                _status = newStatus;
            }

            if (oldStatus != newStatus)
            {
                _logManager.LogInfo($"服务状态变更: {oldStatus} -> {newStatus}, 原因: {reason}");

                var eventArgs = new ServiceStatusChangedEventArgs(oldStatus, newStatus, reason);

                // 触发新接口事件
                ServiceStatusChanged?.Invoke(this, eventArgs);

                // 为了兼容性，也触发旧事件
                StatusChanged?.Invoke(this, eventArgs);
            }
        }

        /// <summary>
        /// 判断是否为严重错误
        /// </summary>
        private bool IsCriticalError(Exception ex)
        {
            return ex is OutOfMemoryException ||
                   ex is StackOverflowException ||
                   ex is AccessViolationException;
        }

        /// <summary>
        /// 检查Dify连接
        /// </summary>
        public async Task<bool> CheckDifyConnectionAsync()
        {
            try
            {
                return await _difyClient.CheckConnectionAsync();
            }
            catch (Exception ex)
            {
                _logManager.LogError("检查Dify连接失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 检查企业微信连接
        /// </summary>
        public async Task<bool> CheckWeChatConnectionAsync()
        {
            try
            {
                return await _wechatBridge.CheckConnectionAsync();
            }
            catch (Exception ex)
            {
                _logManager.LogError("检查企业微信连接失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取运行统计信息
        /// </summary>
        public ServiceStatistics GetStatistics()
        {
            // 这里应该实现真实的统计信息收集
            // 暂时返回模拟数据
            return new ServiceStatistics
            {
                Uptime = DateTime.Now - DateTime.Today, // 简化实现
                MessagesReceived = 0,
                MessagesSent = 0,
                ErrorCount = 0,
                LastActivity = DateTime.Now,
                DifyConnected = true, // 应该从实际状态获取
                WeChatConnected = true // 应该从实际状态获取
            };
        }

        /// <summary>
        /// 发送消息
        /// </summary>
        public async Task<bool> SendMessageAsync(string message, string userId = null)
        {
            try
            {
                // 这里应该实现实际的消息发送逻辑
                _logManager.LogInfo($"发送消息: {message} 到用户: {userId ?? "默认"}");
                return true; // 简化实现
            }
            catch (Exception ex)
            {
                _logManager.LogError("发送消息失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 处理消息接收事件
        /// </summary>
        private void OnMessageReceived(object sender, MessageReceivedEventArgs e)
        {
            try
            {
                _logManager.LogInfo($"收到消息: {e.Message?.Content}");

                // 转发消息接收事件
                MessageReceived?.Invoke(this, e);

                // 处理消息
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await _messageProcessor.ProcessMessageAsync(e.Message);
                    }
                    catch (Exception ex)
                    {
                        _logManager.LogError("处理消息失败", ex);
                        ErrorOccurred?.Invoke(this, new ErrorOccurredEventArgs(ex.Message, ex));
                    }
                });
            }
            catch (Exception ex)
            {
                _logManager.LogError("消息接收事件处理失败", ex);
                ErrorOccurred?.Invoke(this, new ErrorOccurredEventArgs(ex.Message, ex));
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_wechatBridge != null)
            {
                _wechatBridge.MessageReceived -= OnMessageReceived;
            }

            _cancellationTokenSource?.Cancel();
            _cancellationTokenSource?.Dispose();
            
            _serviceTask?.Wait(TimeSpan.FromSeconds(5));
        }
    }
}