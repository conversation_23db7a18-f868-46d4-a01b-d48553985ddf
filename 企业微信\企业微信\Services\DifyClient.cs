using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Diagnostics;
using Newtonsoft.Json;
using 企业微信.Models;

namespace 企业微信.Services
{
    /// <summary>
    /// Dify API客户端
    /// </summary>
    public class DifyClient : IDifyClient, IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly IConfigManager _configManager;
        private readonly ILogManager _logManager;
        private DifyConfig _config;

        public DifyClient(IConfigManager configManager, ILogManager logManager)
        {
            _configManager = configManager ?? throw new ArgumentNullException(nameof(configManager));
            _logManager = logManager ?? throw new ArgumentNullException(nameof(logManager));
            
            _httpClient = new HttpClient();
            _config = _configManager.GetConfig().Dify;
            
            // 监听配置变更
            _configManager.ConfigChanged += OnConfigChanged;
            
            UpdateHttpClientConfig();
        }

        /// <summary>
        /// 发送消息到Dify
        /// </summary>
        public async Task<string> SendMessageAsync(string message, string userId = null)
        {
            if (string.IsNullOrEmpty(message))
                throw new ArgumentException("消息内容不能为空", nameof(message));

            if (!_config.Enabled)
                throw new InvalidOperationException("Dify服务未启用");

            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                _logManager.LogInfo($"发送消息到Dify: {message.Substring(0, Math.Min(message.Length, 100))}...");

                var requestData = new
                {
                    inputs = new { },
                    query = message,
                    response_mode = "blocking",
                    conversation_id = "",
                    user = userId ?? "default_user"
                };

                var json = JsonConvert.SerializeObject(requestData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync("/chat-messages", content);
                
                stopwatch.Stop();

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = ParseDifyResponse(responseContent);
                    
                    _logManager.LogInfo($"Dify响应成功，耗时: {stopwatch.ElapsedMilliseconds}ms");
                    return result;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    var errorMessage = $"Dify API调用失败: {response.StatusCode}, {errorContent}";
                    _logManager.LogError(errorMessage);
                    throw new HttpRequestException(errorMessage);
                }
            }
            catch (TaskCanceledException ex)
            {
                stopwatch.Stop();
                var errorMessage = $"Dify API调用超时，耗时: {stopwatch.ElapsedMilliseconds}ms";
                _logManager.LogError(errorMessage, ex);
                throw new TimeoutException(errorMessage, ex);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                var errorMessage = $"Dify API调用异常，耗时: {stopwatch.ElapsedMilliseconds}ms";
                _logManager.LogError(errorMessage, ex);
                throw;
            }
        }

        /// <summary>
        /// 检查连接状态
        /// </summary>
        public async Task<bool> CheckConnectionAsync()
        {
            try
            {
                if (!_config.Enabled)
                    return false;

                var response = await _httpClient.GetAsync("/parameters");
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logManager.LogError("检查Dify连接状态失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取API状态
        /// </summary>
        public async Task<ApiStatus> GetApiStatusAsync()
        {
            var stopwatch = Stopwatch.StartNew();
            var status = new ApiStatus();

            try
            {
                if (!_config.Enabled)
                {
                    status.IsConnected = false;
                    status.ErrorMessage = "Dify服务未启用";
                    return status;
                }

                var response = await _httpClient.GetAsync("/parameters");
                stopwatch.Stop();

                status.IsConnected = response.IsSuccessStatusCode;
                status.ResponseTimeMs = stopwatch.ElapsedMilliseconds;

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    // 尝试解析版本信息
                    try
                    {
                        var data = JsonConvert.DeserializeObject<dynamic>(content);
                        status.Version = data?.version?.ToString() ?? "Unknown";
                    }
                    catch
                    {
                        status.Version = "Unknown";
                    }
                }
                else
                {
                    status.ErrorMessage = $"HTTP {response.StatusCode}: {response.ReasonPhrase}";
                }
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                status.IsConnected = false;
                status.ResponseTimeMs = stopwatch.ElapsedMilliseconds;
                status.ErrorMessage = ex.Message;
            }

            return status;
        }

        /// <summary>
        /// 解析Dify响应
        /// </summary>
        private string ParseDifyResponse(string responseContent)
        {
            try
            {
                var response = JsonConvert.DeserializeObject<dynamic>(responseContent);
                
                // 检查是否有错误
                if (response?.error != null)
                {
                    throw new InvalidOperationException($"Dify返回错误: {response.error}");
                }

                // 提取回复内容
                string answer = response?.answer?.ToString();
                if (!string.IsNullOrEmpty(answer))
                {
                    return answer;
                }

                // 如果没有answer字段，尝试其他可能的字段
                string message = response?.message?.ToString();
                if (!string.IsNullOrEmpty(message))
                {
                    return message;
                }

                // 如果都没有，返回原始响应
                return responseContent;
            }
            catch (JsonException ex)
            {
                _logManager.LogWarning($"解析Dify响应失败，返回原始内容: {ex.Message}");
                return responseContent;
            }
        }

        /// <summary>
        /// 配置变更处理
        /// </summary>
        private void OnConfigChanged(object sender, ConfigChangedEventArgs e)
        {
            _config = e.NewConfig.Dify;
            UpdateHttpClientConfig();
            _logManager.LogInfo("Dify客户端配置已更新");
        }

        /// <summary>
        /// 更新HttpClient配置
        /// </summary>
        private void UpdateHttpClientConfig()
        {
            if (string.IsNullOrEmpty(_config.ApiUrl) || string.IsNullOrEmpty(_config.ApiKey))
                return;

            try
            {
                _httpClient.BaseAddress = new Uri(_config.ApiUrl);
                _httpClient.Timeout = TimeSpan.FromSeconds(_config.TimeoutSeconds);
                
                // 设置请求头
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_config.ApiKey}");
                _httpClient.DefaultRequestHeaders.Add("User-Agent", "Dify-WeChat-Client/1.0");
            }
            catch (Exception ex)
            {
                _logManager.LogError("更新Dify客户端配置失败", ex);
            }
        }

        /// <summary>
        /// 重试机制包装器
        /// </summary>
        private async Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation)
        {
            var retryCount = 0;
            var maxRetries = _config.RetryCount;

            while (retryCount <= maxRetries)
            {
                try
                {
                    return await operation();
                }
                catch (Exception ex) when (retryCount < maxRetries && IsRetryableException(ex))
                {
                    retryCount++;
                    var delay = TimeSpan.FromSeconds(Math.Pow(2, retryCount)); // 指数退避
                    
                    _logManager.LogWarning($"Dify API调用失败，第{retryCount}次重试，{delay.TotalSeconds}秒后重试: {ex.Message}");
                    
                    await Task.Delay(delay);
                }
            }

            // 如果所有重试都失败，抛出最后一个异常
            return await operation();
        }

        /// <summary>
        /// 判断异常是否可重试
        /// </summary>
        private bool IsRetryableException(Exception ex)
        {
            return ex is HttpRequestException ||
                   ex is TaskCanceledException ||
                   (ex is HttpRequestException httpEx && 
                    httpEx.Message.Contains("timeout", StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_configManager != null)
            {
                _configManager.ConfigChanged -= OnConfigChanged;
            }
            
            _httpClient?.Dispose();
        }
    }
}