using System;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace 企业微信.Utils
{
    /// <summary>
    /// JSON序列化帮助类
    /// </summary>
    public static class JsonHelper
    {
        private static readonly JsonSerializerSettings _defaultSettings = new JsonSerializerSettings
        {
            ContractResolver = new CamelCasePropertyNamesContractResolver(),
            DateFormatHandling = DateFormatHandling.IsoDateFormat,
            DateTimeZoneHandling = DateTimeZoneHandling.Local,
            NullValueHandling = NullValueHandling.Ignore,
            DefaultValueHandling = DefaultValueHandling.Ignore,
            Formatting = Formatting.None
        };

        private static readonly JsonSerializerSettings _indentedSettings = new JsonSerializerSettings
        {
            ContractResolver = new CamelCasePropertyNamesContractResolver(),
            DateFormatHandling = DateFormatHandling.IsoDateFormat,
            DateTimeZoneHandling = DateTimeZoneHandling.Local,
            NullValueHandling = NullValueHandling.Ignore,
            DefaultValueHandling = DefaultValueHandling.Ignore,
            Formatting = Formatting.Indented
        };

        /// <summary>
        /// 序列化对象为JSON字符串
        /// </summary>
        public static string Serialize(object obj, bool indented = false)
        {
            if (obj == null) return null;

            try
            {
                var settings = indented ? _indentedSettings : _defaultSettings;
                return JsonConvert.SerializeObject(obj, settings);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"JSON序列化失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 反序列化JSON字符串为对象
        /// </summary>
        public static T Deserialize<T>(string json)
        {
            if (string.IsNullOrWhiteSpace(json)) return default(T);

            try
            {
                return JsonConvert.DeserializeObject<T>(json, _defaultSettings);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"JSON反序列化失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 反序列化JSON字符串为对象（非泛型）
        /// </summary>
        public static object Deserialize(string json, Type type)
        {
            if (string.IsNullOrWhiteSpace(json)) return null;

            try
            {
                return JsonConvert.DeserializeObject(json, type, _defaultSettings);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"JSON反序列化失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 尝试序列化对象
        /// </summary>
        public static bool TrySerialize(object obj, out string json, bool indented = false)
        {
            json = null;
            try
            {
                json = Serialize(obj, indented);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 尝试反序列化JSON字符串
        /// </summary>
        public static bool TryDeserialize<T>(string json, out T result)
        {
            result = default(T);
            try
            {
                result = Deserialize<T>(json);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证JSON字符串格式
        /// </summary>
        public static bool IsValidJson(string json)
        {
            if (string.IsNullOrWhiteSpace(json)) return false;

            try
            {
                JsonConvert.DeserializeObject(json);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 格式化JSON字符串
        /// </summary>
        public static string FormatJson(string json)
        {
            if (string.IsNullOrWhiteSpace(json)) return json;

            try
            {
                var obj = JsonConvert.DeserializeObject(json);
                return JsonConvert.SerializeObject(obj, _indentedSettings);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"JSON格式化失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 压缩JSON字符串（移除空白字符）
        /// </summary>
        public static string CompactJson(string json)
        {
            if (string.IsNullOrWhiteSpace(json)) return json;

            try
            {
                var obj = JsonConvert.DeserializeObject(json);
                return JsonConvert.SerializeObject(obj, _defaultSettings);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"JSON压缩失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 深度克隆对象（通过JSON序列化/反序列化）
        /// </summary>
        public static T DeepClone<T>(T obj)
        {
            if (obj == null) return default(T);

            try
            {
                var json = Serialize(obj);
                return Deserialize<T>(json);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"对象深度克隆失败: {ex.Message}", ex);
            }
        }
    }
}