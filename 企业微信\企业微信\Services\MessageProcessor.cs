using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using 企业微信.Models;

namespace 企业微信.Services
{
    /// <summary>
    /// 消息处理器
    /// </summary>
    public class MessageProcessor : IMessageProcessor
    {
        private readonly IConfigManager _configManager;
        private readonly ILogManager _logManager;
        private readonly IDifyClient _difyClient;
        private readonly IWeChatBridge _wechatBridge;

        public event EventHandler<MessageProcessedEventArgs> MessageProcessed;

        public MessageProcessor(
            IConfigManager configManager,
            ILogManager logManager,
            IDifyClient difyClient,
            IWeChatBridge wechatBridge)
        {
            _configManager = configManager ?? throw new ArgumentNullException(nameof(configManager));
            _logManager = logManager ?? throw new ArgumentNullException(nameof(logManager));
            _difyClient = difyClient ?? throw new ArgumentNullException(nameof(difyClient));
            _wechatBridge = wechatBridge ?? throw new ArgumentNullException(nameof(wechatBridge));
        }

        /// <summary>
        /// 处理消息
        /// </summary>
        public async Task<MessageModel> ProcessMessageAsync(MessageModel message)
        {
            if (message == null)
            {
                throw new ArgumentNullException(nameof(message));
            }

            try
            {
                _logManager.LogInfo($"开始处理消息: {message.Id}");
                message.Status = MessageStatus.Processing;

                var config = _configManager.GetConfig();

                // 1. 消息预处理
                var preprocessedMessage = PreprocessMessage(message, config);

                // 2. 检查是否需要处理
                if (!ShouldProcessMessage(preprocessedMessage, config))
                {
                    preprocessedMessage.Status = MessageStatus.Ignored;
                    _logManager.LogInfo($"消息被忽略: {message.Id}");
                    return preprocessedMessage;
                }

                // 3. 检查自定义回复
                var customReply = CheckCustomReply(preprocessedMessage, config);
                if (!string.IsNullOrEmpty(customReply))
                {
                    await SendReplyAsync(preprocessedMessage, customReply);
                    preprocessedMessage.Status = MessageStatus.Completed;
                    _logManager.LogInfo($"使用自定义回复处理消息: {message.Id}");
                    return preprocessedMessage;
                }

                // 4. 发送到Dify处理
                if (config.Dify.Enabled)
                {
                    var difyResponse = await _difyClient.SendMessageAsync(preprocessedMessage.Content, preprocessedMessage.SenderId);
                    
                    if (!string.IsNullOrEmpty(difyResponse))
                    {
                        await SendReplyAsync(preprocessedMessage, difyResponse);
                        preprocessedMessage.Status = MessageStatus.Completed;
                        _logManager.LogInfo($"Dify处理消息成功: {message.Id}");
                    }
                    else
                    {
                        preprocessedMessage.Status = MessageStatus.Failed;
                        preprocessedMessage.ErrorMessage = "Dify返回空响应";
                        _logManager.LogWarning($"Dify返回空响应: {message.Id}");
                    }
                }
                else
                {
                    // 如果Dify未启用，使用默认回复
                    var defaultReply = config.Message.DefaultReply ?? "抱歉，我暂时无法处理您的消息。";
                    await SendReplyAsync(preprocessedMessage, defaultReply);
                    preprocessedMessage.Status = MessageStatus.Completed;
                    _logManager.LogInfo($"使用默认回复处理消息: {message.Id}");
                }

                // 触发处理完成事件
                MessageProcessed?.Invoke(this, new MessageProcessedEventArgs { Message = preprocessedMessage });

                return preprocessedMessage;
            }
            catch (Exception ex)
            {
                message.Status = MessageStatus.Failed;
                message.ErrorMessage = ex.Message;
                _logManager.LogError($"处理消息失败: {message.Id}", ex);
                return message;
            }
        }

        /// <summary>
        /// 批量处理消息
        /// </summary>
        public async Task<List<MessageModel>> ProcessMessagesAsync(List<MessageModel> messages)
        {
            if (messages == null || !messages.Any())
            {
                return new List<MessageModel>();
            }

            var results = new List<MessageModel>();
            var config = _configManager.GetConfig();

            _logManager.LogInfo($"开始批量处理消息，数量: {messages.Count}");

            foreach (var message in messages)
            {
                try
                {
                    var result = await ProcessMessageAsync(message);
                    results.Add(result);

                    // 添加处理间隔，避免过于频繁的请求
                    if (config.Message.ProcessingDelayMs > 0)
                    {
                        await Task.Delay(config.Message.ProcessingDelayMs);
                    }
                }
                catch (Exception ex)
                {
                    _logManager.LogError($"批量处理消息异常: {message.Id}", ex);
                    message.Status = MessageStatus.Failed;
                    message.ErrorMessage = ex.Message;
                    results.Add(message);
                }
            }

            _logManager.LogInfo($"批量处理消息完成，成功: {results.Count(r => r.Status == MessageStatus.Completed)}, 失败: {results.Count(r => r.Status == MessageStatus.Failed)}");

            return results;
        }

        /// <summary>
        /// 消息预处理
        /// </summary>
        private MessageModel PreprocessMessage(MessageModel message, AppConfig config)
        {
            // 清理消息内容
            var cleanedContent = message.Content?.Trim();
            
            // 移除多余的空白字符
            if (!string.IsNullOrEmpty(cleanedContent))
            {
                cleanedContent = Regex.Replace(cleanedContent, @"\s+", " ");
            }

            message.Content = cleanedContent;
            return message;
        }

        /// <summary>
        /// 判断是否需要处理消息
        /// </summary>
        private bool ShouldProcessMessage(MessageModel message, AppConfig config)
        {
            // 检查消息类型
            if (message.Type != MessageType.Text)
            {
                return false;
            }

            // 检查消息方向
            if (message.Direction != MessageDirection.Incoming)
            {
                return false;
            }

            // 检查消息内容
            if (string.IsNullOrWhiteSpace(message.Content))
            {
                return false;
            }

            // 检查消息长度限制
            if (config.Message.MaxMessageLength > 0 && message.Content.Length > config.Message.MaxMessageLength)
            {
                _logManager.LogWarning($"消息超过长度限制: {message.Content.Length} > {config.Message.MaxMessageLength}");
                return false;
            }

            // 检查关键词过滤
            if (config.Message.FilterKeywords != null && config.Message.FilterKeywords.Any())
            {
                var lowerContent = message.Content.ToLower();
                if (config.Message.FilterKeywords.Any(keyword => lowerContent.Contains(keyword.ToLower())))
                {
                    _logManager.LogInfo($"消息包含过滤关键词，跳过处理: {message.Id}");
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 检查自定义回复
        /// </summary>
        private string CheckCustomReply(MessageModel message, AppConfig config)
        {
            if (config.CustomReply?.Rules == null || !config.CustomReply.Rules.Any())
            {
                return null;
            }

            var messageContent = message.Content.ToLower();

            foreach (var rule in config.CustomReply.Rules)
            {
                if (string.IsNullOrEmpty(rule.Pattern) || string.IsNullOrEmpty(rule.Reply))
                {
                    continue;
                }

                bool isMatch = false;

                switch (rule.MatchType)
                {
                    case MatchType.Exact:
                        isMatch = messageContent.Equals(rule.Pattern.ToLower());
                        break;

                    case MatchType.Contains:
                        isMatch = messageContent.Contains(rule.Pattern.ToLower());
                        break;

                    case MatchType.StartsWith:
                        isMatch = messageContent.StartsWith(rule.Pattern.ToLower());
                        break;

                    case MatchType.EndsWith:
                        isMatch = messageContent.EndsWith(rule.Pattern.ToLower());
                        break;

                    case MatchType.Regex:
                        try
                        {
                            isMatch = Regex.IsMatch(messageContent, rule.Pattern, RegexOptions.IgnoreCase);
                        }
                        catch (Exception ex)
                        {
                            _logManager.LogError($"正则表达式匹配失败: {rule.Pattern}", ex);
                        }
                        break;
                }

                if (isMatch)
                {
                    _logManager.LogInfo($"匹配自定义回复规则: {rule.Pattern}");
                    return rule.Reply;
                }
            }

            return null;
        }

        /// <summary>
        /// 发送回复
        /// </summary>
        private async Task SendReplyAsync(MessageModel originalMessage, string replyContent)
        {
            try
            {
                var replyMessage = new MessageModel
                {
                    Id = Guid.NewGuid().ToString(),
                    SenderId = "system", // 系统发送
                    ReceiverId = originalMessage.SenderId,
                    Content = replyContent,
                    Type = MessageType.Text,
                    Direction = MessageDirection.Outgoing,
                    CreatedAt = DateTime.Now,
                    Status = MessageStatus.Pending
                };

                var success = await _wechatBridge.SendMessageAsync(replyMessage);
                
                if (success)
                {
                    replyMessage.Status = MessageStatus.Completed;
                    _logManager.LogInfo($"回复消息发送成功: {replyMessage.Id}");
                }
                else
                {
                    replyMessage.Status = MessageStatus.Failed;
                    _logManager.LogError($"回复消息发送失败: {replyMessage.Id}");
                }
            }
            catch (Exception ex)
            {
                _logManager.LogError("发送回复消息异常", ex);
                throw;
            }
        }
    }
}