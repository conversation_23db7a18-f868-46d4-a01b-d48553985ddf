using System;
using System.Collections.Generic;

namespace 企业微信.Models
{
    /// <summary>
    /// 日志管理器接口
    /// </summary>
    public interface ILogManager : IDisposable
    {
        /// <summary>
        /// 日志添加事件
        /// </summary>
        event EventHandler<LogAddedEventArgs> LogAdded;

        /// <summary>
        /// 记录调试日志
        /// </summary>
        void LogDebug(string message, string source = null);

        /// <summary>
        /// 记录信息日志
        /// </summary>
        void LogInfo(string message, string source = null);

        /// <summary>
        /// 记录警告日志
        /// </summary>
        void LogWarning(string message, string source = null);

        /// <summary>
        /// 记录错误日志
        /// </summary>
        void LogError(string message, Exception exception = null, string source = null);

        /// <summary>
        /// 记录致命错误日志
        /// </summary>
        void LogFatal(string message, Exception exception = null, string source = null);

        /// <summary>
        /// 获取最近的日志条目
        /// </summary>
        List<LogEntry> GetRecentLogs(int count = 100);

        /// <summary>
        /// 清空日志
        /// </summary>
        void ClearLogs();

        /// <summary>
        /// 设置日志级别
        /// </summary>
        void SetLogLevel(LogLevel level);

        /// <summary>
        /// 获取当前日志级别
        /// </summary>
        LogLevel GetLogLevel();
    }
}