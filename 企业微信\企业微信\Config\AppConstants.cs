using System;

namespace 企业微信.Config
{
    /// <summary>
    /// 应用常量定义
    /// </summary>
    public static class AppConstants
    {
        #region 应用信息
        /// <summary>
        /// 应用名称
        /// </summary>
        public const string APP_NAME = "企业微信智能助手";

        /// <summary>
        /// 应用版本
        /// </summary>
        public const string APP_VERSION = "1.0.0";

        /// <summary>
        /// 应用描述
        /// </summary>
        public const string APP_DESCRIPTION = "基于Dify的企业微信智能客服助手";

        /// <summary>
        /// 开发者信息
        /// </summary>
        public const string DEVELOPER = "AI Assistant";
        #endregion

        #region 文件路径
        /// <summary>
        /// 配置文件名
        /// </summary>
        public const string CONFIG_FILE_NAME = "config.json";

        /// <summary>
        /// 环境配置文件名
        /// </summary>
        public const string ENV_FILE_NAME = ".env";

        /// <summary>
        /// 日志文件夹名
        /// </summary>
        public const string LOGS_FOLDER_NAME = "Logs";

        /// <summary>
        /// 配置文件夹名
        /// </summary>
        public const string CONFIG_FOLDER_NAME = "Config";

        /// <summary>
        /// 数据文件夹名
        /// </summary>
        public const string DATA_FOLDER_NAME = "Data";

        /// <summary>
        /// 临时文件夹名
        /// </summary>
        public const string TEMP_FOLDER_NAME = "Temp";
        #endregion

        #region 默认配置值
        /// <summary>
        /// 默认HTTP超时时间（毫秒）
        /// </summary>
        public const int DEFAULT_HTTP_TIMEOUT = 30000;

        /// <summary>
        /// 默认重试次数
        /// </summary>
        public const int DEFAULT_RETRY_COUNT = 3;

        /// <summary>
        /// 默认重试间隔（毫秒）
        /// </summary>
        public const int DEFAULT_RETRY_INTERVAL = 1000;

        /// <summary>
        /// 默认日志保留天数
        /// </summary>
        public const int DEFAULT_LOG_RETENTION_DAYS = 30;

        /// <summary>
        /// 默认消息队列大小
        /// </summary>
        public const int DEFAULT_MESSAGE_QUEUE_SIZE = 1000;

        /// <summary>
        /// 默认批处理大小
        /// </summary>
        public const int DEFAULT_BATCH_SIZE = 10;

        /// <summary>
        /// 默认健康检查间隔（毫秒）
        /// </summary>
        public const int DEFAULT_HEALTH_CHECK_INTERVAL = 60000;

        /// <summary>
        /// 默认配置刷新间隔（毫秒）
        /// </summary>
        public const int DEFAULT_CONFIG_REFRESH_INTERVAL = 300000;
        #endregion

        #region API相关
        /// <summary>
        /// Dify API默认版本
        /// </summary>
        public const string DIFY_API_VERSION = "v1";

        /// <summary>
        /// 企业微信API基础URL
        /// </summary>
        public const string WECHAT_API_BASE_URL = "https://qyapi.weixin.qq.com";

        /// <summary>
        /// 默认User-Agent
        /// </summary>
        public const string DEFAULT_USER_AGENT = "WeChatBot/1.0.0";

        /// <summary>
        /// 默认Content-Type
        /// </summary>
        public const string DEFAULT_CONTENT_TYPE = "application/json";

        /// <summary>
        /// 访问令牌有效期（秒）
        /// </summary>
        public const int ACCESS_TOKEN_EXPIRES_IN = 7200;

        /// <summary>
        /// 访问令牌刷新提前时间（秒）
        /// </summary>
        public const int ACCESS_TOKEN_REFRESH_ADVANCE = 300;
        #endregion

        #region 消息相关
        /// <summary>
        /// 最大消息长度
        /// </summary>
        public const int MAX_MESSAGE_LENGTH = 2048;

        /// <summary>
        /// 最大文件大小（字节）
        /// </summary>
        public const long MAX_FILE_SIZE = 20 * 1024 * 1024; // 20MB

        /// <summary>
        /// 支持的图片格式
        /// </summary>
        public static readonly string[] SUPPORTED_IMAGE_FORMATS = { ".jpg", ".jpeg", ".png", ".gif", ".bmp" };

        /// <summary>
        /// 支持的文档格式
        /// </summary>
        public static readonly string[] SUPPORTED_DOCUMENT_FORMATS = { ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".txt" };

        /// <summary>
        /// 消息处理超时时间（毫秒）
        /// </summary>
        public const int MESSAGE_PROCESSING_TIMEOUT = 30000;
        #endregion

        #region 日志相关
        /// <summary>
        /// 日志文件最大大小（字节）
        /// </summary>
        public const long MAX_LOG_FILE_SIZE = 10 * 1024 * 1024; // 10MB

        /// <summary>
        /// 日志文件名格式
        /// </summary>
        public const string LOG_FILE_NAME_FORMAT = "app_{0:yyyyMMdd}.log";

        /// <summary>
        /// 错误日志文件名格式
        /// </summary>
        public const string ERROR_LOG_FILE_NAME_FORMAT = "error_{0:yyyyMMdd}.log";

        /// <summary>
        /// 日志时间格式
        /// </summary>
        public const string LOG_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss.fff";
        #endregion

        #region 状态码
        /// <summary>
        /// 成功状态码
        /// </summary>
        public const int SUCCESS_CODE = 0;

        /// <summary>
        /// 一般错误状态码
        /// </summary>
        public const int ERROR_CODE = -1;

        /// <summary>
        /// 参数错误状态码
        /// </summary>
        public const int PARAMETER_ERROR_CODE = -2;

        /// <summary>
        /// 网络错误状态码
        /// </summary>
        public const int NETWORK_ERROR_CODE = -3;

        /// <summary>
        /// 认证错误状态码
        /// </summary>
        public const int AUTH_ERROR_CODE = -4;

        /// <summary>
        /// 权限错误状态码
        /// </summary>
        public const int PERMISSION_ERROR_CODE = -5;

        /// <summary>
        /// 服务不可用状态码
        /// </summary>
        public const int SERVICE_UNAVAILABLE_CODE = -6;
        #endregion

        #region 正则表达式
        /// <summary>
        /// URL验证正则表达式
        /// </summary>
        public const string URL_REGEX = @"^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$";

        /// <summary>
        /// 邮箱验证正则表达式
        /// </summary>
        public const string EMAIL_REGEX = @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$";

        /// <summary>
        /// 手机号验证正则表达式
        /// </summary>
        public const string PHONE_REGEX = @"^1[3-9]\d{9}$";

        /// <summary>
        /// IP地址验证正则表达式
        /// </summary>
        public const string IP_REGEX = @"^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";
        #endregion

        #region 缓存相关
        /// <summary>
        /// 默认缓存过期时间（分钟）
        /// </summary>
        public const int DEFAULT_CACHE_EXPIRY_MINUTES = 30;

        /// <summary>
        /// 配置缓存键前缀
        /// </summary>
        public const string CONFIG_CACHE_KEY_PREFIX = "config:";

        /// <summary>
        /// 用户信息缓存键前缀
        /// </summary>
        public const string USER_CACHE_KEY_PREFIX = "user:";

        /// <summary>
        /// 访问令牌缓存键
        /// </summary>
        public const string ACCESS_TOKEN_CACHE_KEY = "access_token";
        #endregion

        #region 事件名称
        /// <summary>
        /// 配置变更事件
        /// </summary>
        public const string CONFIG_CHANGED_EVENT = "ConfigChanged";

        /// <summary>
        /// 服务状态变更事件
        /// </summary>
        public const string SERVICE_STATUS_CHANGED_EVENT = "ServiceStatusChanged";

        /// <summary>
        /// 消息接收事件
        /// </summary>
        public const string MESSAGE_RECEIVED_EVENT = "MessageReceived";

        /// <summary>
        /// 消息发送事件
        /// </summary>
        public const string MESSAGE_SENT_EVENT = "MessageSent";

        /// <summary>
        /// 错误发生事件
        /// </summary>
        public const string ERROR_OCCURRED_EVENT = "ErrorOccurred";
        #endregion

        #region 环境变量名
        /// <summary>
        /// Dify API URL环境变量名
        /// </summary>
        public const string ENV_DIFY_API_URL = "DIFY_API_URL";

        /// <summary>
        /// Dify API Key环境变量名
        /// </summary>
        public const string ENV_DIFY_API_KEY = "DIFY_API_KEY";

        /// <summary>
        /// 企业微信Corp ID环境变量名
        /// </summary>
        public const string ENV_WECHAT_CORP_ID = "WECHAT_CORP_ID";

        /// <summary>
        /// 企业微信Corp Secret环境变量名
        /// </summary>
        public const string ENV_WECHAT_CORP_SECRET = "WECHAT_CORP_SECRET";

        /// <summary>
        /// 企业微信Agent ID环境变量名
        /// </summary>
        public const string ENV_WECHAT_AGENT_ID = "WECHAT_AGENT_ID";

        /// <summary>
        /// 日志级别环境变量名
        /// </summary>
        public const string ENV_LOG_LEVEL = "LOG_LEVEL";

        /// <summary>
        /// 调试模式环境变量名
        /// </summary>
        public const string ENV_DEBUG_MODE = "DEBUG_MODE";
        #endregion

        #region 时间相关
        /// <summary>
        /// 标准日期时间格式
        /// </summary>
        public const string STANDARD_DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

        /// <summary>
        /// 标准日期格式
        /// </summary>
        public const string STANDARD_DATE_FORMAT = "yyyy-MM-dd";

        /// <summary>
        /// 标准时间格式
        /// </summary>
        public const string STANDARD_TIME_FORMAT = "HH:mm:ss";

        /// <summary>
        /// ISO 8601日期时间格式
        /// </summary>
        public const string ISO8601_DATETIME_FORMAT = "yyyy-MM-ddTHH:mm:ss.fffZ";
        #endregion

        #region 编码相关
        /// <summary>
        /// 默认字符编码
        /// </summary>
        public const string DEFAULT_ENCODING = "UTF-8";

        /// <summary>
        /// JSON编码
        /// </summary>
        public const string JSON_ENCODING = "application/json; charset=utf-8";
        #endregion
    }
}