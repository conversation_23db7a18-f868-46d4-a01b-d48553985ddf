using System;

namespace 企业微信.Models
{
    /// <summary>
    /// 应用程序配置
    /// </summary>
    public class AppConfig
    {
        public DifyConfig DifyConfig { get; set; } = new DifyConfig();
        public WeChatConfig WeChatConfig { get; set; } = new WeChatConfig();
        public AppSettings AppSettings { get; set; } = new AppSettings();
    }

    /// <summary>
    /// Dify配置
    /// </summary>
    public class DifyConfig
    {
        public string ApiUrl { get; set; } = "";
        public string ApiKey { get; set; } = "";
        public string UserId { get; set; } = "";
        public int TimeoutSeconds { get; set; } = 30;
        public bool EnableStreaming { get; set; } = false;
    }

    /// <summary>
    /// 企业微信配置
    /// </summary>
    public class WeChatConfig
    {
        public string CorpId { get; set; } = "";
        public string Secret { get; set; } = "";
        public int AgentId { get; set; } = 0;
        public string Token { get; set; } = "";
        public string EncodingAESKey { get; set; } = "";
        public int CallbackPort { get; set; } = 8080;
    }

    /// <summary>
    /// 应用设置
    /// </summary>
    public class AppSettings
    {
        public bool AutoStart { get; set; } = false;
        public bool MinimizeToTray { get; set; } = true;
        public bool EnableLogging { get; set; } = true;
        public LogLevel LogLevel { get; set; } = LogLevel.Info;
        public int MaxLogFiles { get; set; } = 10;
        public int LogRetentionDays { get; set; } = 30;
        public bool EnableNotifications { get; set; } = true;
        public int MessageHistoryLimit { get; set; } = 1000;
        public int StatusUpdateInterval { get; set; } = 5000;
    }
}