using System;
using Newtonsoft.Json;

namespace 企业微信.Models
{
    /// <summary>
    /// 消息模型
    /// </summary>
    public class MessageModel
    {
        /// <summary>
        /// 消息ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 发送者ID
        /// </summary>
        public string SenderId { get; set; }

        /// <summary>
        /// 发送者姓名
        /// </summary>
        public string SenderName { get; set; }

        /// <summary>
        /// 接收者ID
        /// </summary>
        public string ReceiverId { get; set; }

        /// <summary>
        /// 消息内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 消息类型
        /// </summary>
        public MessageType Type { get; set; } = MessageType.Text;

        /// <summary>
        /// 消息方向
        /// </summary>
        public MessageDirection Direction { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 处理状态
        /// </summary>
        public MessageStatus Status { get; set; } = MessageStatus.Pending;

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 响应时间（毫秒）
        /// </summary>
        public long ResponseTimeMs { get; set; }
    }

    /// <summary>
    /// 消息类型枚举
    /// </summary>
    public enum MessageType
    {
        Text = 1,
        Image = 2,
        Voice = 3,
        Video = 4,
        File = 5
    }

    /// <summary>
    /// 消息方向枚举
    /// </summary>
    public enum MessageDirection
    {
        Incoming = 1,  // 接收的消息
        Outgoing = 2   // 发送的消息
    }

    /// <summary>
    /// 消息状态枚举
    /// </summary>
    public enum MessageStatus
    {
        Pending = 1,    // 待处理
        Processing = 2, // 处理中
        Success = 3,    // 成功
        Failed = 4      // 失败
    }
}